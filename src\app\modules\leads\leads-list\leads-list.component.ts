// MODULES
import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { Router } from '@angular/router';
import { saveAs } from 'file-saver';
import {
  debounceTime,
  switchMap,
  finalize,
  retry,
  takeUntil,
} from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import jwt_decode from 'jwt-decode';
import { MatTabChangeEvent } from '@angular/material/tabs';

// SERVICES
import { LeadsService } from 'src/app/providers/leads/leads.service';
import { SharedService } from 'src/app/shared/shared.service';
import { CommonFunctionsService } from 'src/app/utils/common-functions/common-functions.service';
import { UserService } from 'src/app/providers/user/user.service';
import { InventoryService } from 'src/app/providers/inventory/inventory.service';

// COMPONENTS
import { GridViewComponent } from 'src/app/shared/grid-view/grid-view.component';
import { ListViewComponent } from 'src/app/shared/list-view/list-view.component';
import { LeadsFilterComponent } from './leads-filter/leads-filter.component';
import { SearchDialogComponent } from 'src/app/shared/dialog/search-dialog/search-dialog.component';
import { ConfirmationDialogComponent } from 'src/app/shared/dialog/confirmation-dialog/confirmation-dialog.component';
import { UnderMaintenanceComponent } from 'src/app/shared/under-maintenance/under-maintenance.component';
import { FilterBarComponent } from 'src/app/shared/filter-bar/filter-bar.component';
import { UpdateMultipleDialogComponent } from './update-multiple-dialog/update-multiple-dialog.component';
import { MasterFilterComponent } from 'src/app/shared/master-filter/master-filter.component';
import { LeadDialerComponent } from 'src/app/shared/dialog/lead-dialer/lead-dialer.component';
import { LeadsStatusChangeComponent } from './leads-status-change/leads-status-change.component';
import { TaskDripStatusChangeComponent } from './task-drip-status-change/task-drip-status-change.component';

// UTILS
import { MessageConstant } from 'src/app/utils/message-constant';
import { ErrorModel } from 'src/app/utils/models/error';
import { ResponseModel } from 'src/app/utils/models/response';
import { environment } from 'src/environments/environment';
import { StatusConstant } from 'src/app/utils/status-constant';
import { MiscellaneousConstant } from 'src/app/utils/miscellaneous-constant';
import { ExplainersPopUpService } from 'src/app/utils/explainers/explainer-popup.service';
import { OnboardingCallDailogComponent } from 'src/app/shared/dialog/onboarding-call-dailog/onboarding-call-dailog.component';
import { FirstTimeLoginComponent } from 'src/app/shared/dialog/first-time-login/first-time-login.component';
import { DlcMessageDialogComponent } from 'src/app/shared/dialog/dlc-message-dialog/dlc-message-dialog.component';
import {
  LeadsHeaderConfig,
  LeadsHeaders,
} from 'src/app/utils/leads-header-constant';
import { AdvancedListViewComponent } from 'src/app/shared/advanced-list-view/advanced-list-view.component';
import { TypeformDialogComponent } from 'src/app/shared/dialog/typeform-dialog/typeform-dialog.component';
import { LeadsStatusChangeModel } from 'src/app/utils/models/leads-status-change';
import { LeadsInfoComponent } from 'src/app/shared/leads-info/leads-info.component';
import { WholesalePipelineStatusChangeComponent } from '../../wholesale-pipeline/wholesale-pipeline-list/wholesale-pipeline-status-change/wholesale-pipeline-status-change.component';
import { ListingComponent } from '../leads-details/listing/listing.component';

declare var $: any;

@Component({
  selector: 'app-leads-list',
  templateUrl: './leads-list.component.html',
  styleUrls: ['./leads-list.component.scss'],
  standalone: false,
})
export class LeadsListComponent implements OnInit, OnDestroy {
  @ViewChild('leadsInfoComponent') leadsInfoComponent: LeadsInfoComponent;
  @ViewChild('gridView') gridView: GridViewComponent;
  @ViewChild('listView') listView: ListViewComponent;
  @ViewChild('listingComponent') listingComponent: ListingComponent;
  @ViewChild('advancedlistView') advancedlistView: AdvancedListViewComponent;
  @ViewChild('underMaintenance') underMaintenance: UnderMaintenanceComponent;
  @ViewChild('topBar') topBar: FilterBarComponent;
  @ViewChild('masterFilter') masterFilter: MasterFilterComponent;

  messageConstant = MessageConstant;

  leadIcon: string = '/assets/images/leads.svg';
  moduleName: string = 'Leads';
  moduleAddName: string = 'Lead';
  type: string = 'leads';
  leadId: string = '';
  moduleId: string = StatusConstant.MainStatusId.LEAD;
  isFilterEnabled: boolean = true;
  isSearchEnabled: boolean = false;
  isAddEnabled: boolean = true;
  isViewEnabled: boolean = true;
  isSortEnabled: boolean = true;
  isReduceGrid: boolean = true;
  isWhiteBoardEnable: boolean = false;
  isModuleCountEnabled: boolean = true;
  isbackToLeadEnable: boolean = false;
  isTaskCheck: boolean = false;
  isLoading: boolean = false;
  isDeleteListing: boolean = false;

  isMonthlyGroup: boolean = true;
  isWhiteboardListBy: string = 'all';
  inventory: any = {};

  templates: any[] = [];
  header: any[] = [];
  deadLeadHeader: any[] = [];
  mainUserList: any[] = [];
  mainStatusId: any[] = [];
  mainStatusArray: any[] = [];
  investmentType: any[] = [];
  defaultColumnConfig: any[] = LeadsHeaderConfig;
  defaultColumns: any[] = LeadsHeaders;
  userViewPreferenceData: any;
  leadInfoList: any = {};
  leadCountData: any = {};
  taskAppointmentCount: any;
  loadingColumns: boolean = true;

  // Status-specific column configurations
  statusColumnConfigs: { [key: string]: any[] } = {
    // Default configurations for different lead statuses
    [StatusConstant.LeadStatus.WARM_LEAD]: [],
    [StatusConstant.LeadStatus.DEAD_LEAD]: [],
    [StatusConstant.LeadStatus.REFERRED_TO_AGENT]: [],
    default: LeadsHeaderConfig, // Default configuration for regular leads
  };
  reduceGrid: boolean = this._globalService.reduceGrid
    ? this._globalService.reduceGrid
    : false;

  initializeStatusColumnConfigs(existingConfig = null) {
    const statusKeys = [
      'default',
      StatusConstant.MainStatusId.LEAD,
      StatusConstant.LeadStatus.WARM_LEAD,
      StatusConstant.LeadStatus.DEAD_LEAD,
      StatusConstant.LeadStatus.REFERRED_TO_AGENT,
    ];

    this.statusColumnConfigs = {};
    statusKeys.forEach((key) => {
      const defaultColumns = this.getDefaultColumnsFromConstant();

      defaultColumns.forEach((col) => {
        if (col.label === 'Team Assigned') {
          col.hasRoles = true;

          // Initialize roles array if it doesn't exist
          if (!col.roles) {
            col.roles = Object.entries(MiscellaneousConstant.roleNames).map(
              ([roleId, roleName]) => ({
                roleId,
                roleName,
                isVisible: true,
              })
            );
          }
        }
      });

      const configToUse =
        existingConfig && existingConfig[key]
          ? existingConfig[key]
          : defaultColumns;
      this.statusColumnConfigs[key] = this.enhanceColumns(configToUse);
    });

    this.defaultColumnConfig = this.statusColumnConfigs['default'];
  }
  sort: string = this._globalService.leadSort
    ? this._globalService.leadSort
    : environment.pagination.defaultSort;
  view: string = environment.pagination.defaultView;
  applySort: any = {};
  leads: any = {};
  count: number = 0;
  currentPage: number = 1;
  currentLimit: number = MiscellaneousConstant.paginationLimit.STANDARD;
  paginationType: string = MiscellaneousConstant.paginationType.STANDARD;
  dialogRef: any;
  mainStatus: any[] = [];
  leadType: string = '';

  getLeadSubscribe: any;

  sortMode: string = environment.pagination.defaultSort;
  public listsDataCSV: any;
  isExportEnable: boolean = true;
  isPageScroll: any = {};
  isUnderMaintenance: boolean = false;
  loginUserId: String = '';
  debouncedSaveFilter: (...args: any[]) => void;

  isSelectBarEnabled: boolean = true;
  isActionBarEnabled: boolean = true;
  toggleActionBar: boolean = false;
  selectedItems: any[] = [];
  selectedTo: number = 0;
  selectedFrom: number = 0;

  additionalActions: any = [
    {
      label: 'Add To Dialer',
      event: 'addToDialer',
      img: 'assets/images/quick-filters/call-black.svg',
      isAdd: true,
    },
    {
      label: 'Add Tags',
      event: 'TAGS',
      img: 'assets/images/quick-filters/tag-black.svg',
      isAdd: true,
      inputType: 'CHIPS',
    },
    {
      label: 'Remove Tags',
      event: 'TAGS',
      img: 'assets/images/quick-filters/tag-remove-black.svg',
      isAdd: false,
      inputType: 'LIST',
    },
    {
      label: 'Export Selected',
      event: 'EXPORT',
      img: 'assets/images/quick-filters/export-black.svg',
    },
    {
      label: 'Delete Leads',
      event: 'DELETE',
      img: 'assets/images/quick-filters/delete-red.svg',
    },
  ];

  acquisitionsHeader: any[] = [
    {
      label: 'Property Address',
      key: 'address',
      isClickExists: true,
      isTargetBlank: true,
      event: 'LEAD_DETAILS',
      isTooltipExists: true,
      style: { color: '#008080' },
      innerValue: [
        {
          key: 'title',
          style: {
            color: '#95989a',
          },
        },
      ],
      sort: { type: this.sortMode },
    },
    {
      label: '',
      isActivityIconExists: true,
    },
    {
      label: 'Property Status',
      key: 'mainStatusTitle',
      style: { 'text-transform': 'capitalize' },
      isClickExists: true,
      event: 'LEAD_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Exit Strategy',
      key: 'investmentTitle',
      isClickExists: true,
      event: 'LEAD_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Team Assigned',
      key: 'assigned',
      isClickExists: true,
      isTeamAssign: true,
    },
    {
      label: 'U.C Date - Price',
      key: 'underContractDate',
      pipe: 'DATE',
      isClickExists: true,
      event: 'LEAD_DETAILS',
      // separator: ' ',
      innerValue: [
        {
          key: 'underContractPrice',
          pipe: 'CURRENCY',
          style: {
            color: '#95989a',
          },
        },
      ],
      sort: { type: this.sortMode },
    },
    {
      label: 'Sch. Closing Date',
      key: 'closingDate',
      isClickExists: true,
      event: 'LEAD_DETAILS',
      pipe: 'DATE',
      sort: { type: this.sortMode },
    },
    {
      label: 'Expected Profit',
      key: 'assignmentFee',
      pipe: 'CURRENCY',
      isClickExists: true,
      event: 'LEAD_DETAILS',
      innerValue: [
        {
          key: 'buyer',
          pipe: 'TITLE_CASE',
          style: {
            color: '#95989a',
          },
        },
      ],
      sort: { type: this.sortMode },
    },
  ];
  inventoryHeader: any[] = [
    {
      label: 'Property Address',
      key: 'address',
      isClickExists: true,
      isTargetBlank: true,
      event: 'INVENTORY_DETAILS',
      isTooltipExists: true,
      style: { color: '#008080' },
      innerValue: [
        {
          key: 'title',
          style: {
            color: '#95989a',
          },
        },
      ],
      sort: { type: this.sortMode },
    },
    {
      label: '',
      isActivityIconExists: true,
    },
    {
      label: 'Property Status',
      key: 'mainStatusTitle',
      style: { 'text-transform': 'capitalize' },
      isClickExists: true,
      event: 'INVENTORY_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Exit Strategy',
      key: 'investmentTitle',
      isClickExists: true,
      event: 'INVENTORY_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Purchase Date',
      key: 'purchaseDate',
      pipe: 'DATE',
      isClickExists: true,
      event: 'INVENTORY_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Holding Period',
      key: 'hordingPeriod',
      isClickExists: true,
      event: 'INVENTORY_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Purchase Price',
      key: 'purchasePrice',
      pipe: 'CURRENCY',
      isClickExists: true,
      event: 'INVENTORY_DETAILS',
      sort: { type: this.sortMode },
    },
    {
      label: 'Expected Profit',
      key: 'assignmentFee',
      pipe: 'CURRENCY',
      isClickExists: true,
      event: 'INVENTORY_DETAILS',
      sort: { type: this.sortMode },
    },
  ];
  selectActionType: string = '';
  isMultiSelectEnabled: boolean = true;
  isExportList: boolean = false;
  updateType = {
    DELETE: 1,
    TAGS: 2,
  };
  deadLeadReason: boolean = false;

  private viewChangeSubscription: Subscription;
  private mainStatusSubject = new Subject<void>();

  constructor(
    private _router: Router,
    private _sharedService: SharedService,
    private _loaderService: NgxUiLoaderService,
    private _toastrService: ToastrService,
    private _leadService: LeadsService,
    private _dialog: MatDialog,
    private _route: ActivatedRoute,
    public _globalService: CommonFunctionsService,
    private _userService: UserService,
    public _explainersPopUpService: ExplainersPopUpService,
    private _inventoryService: InventoryService
  ) {
    const decoded: any = jwt_decode(localStorage.getItem('token'));
    this.loginUserId = decoded.userId;
    this.debouncedSaveFilter = this.debounce(this.saveFilter.bind(this), 3000);
  }

  async ngOnInit(): Promise<void> {
    if (this._globalService.isSearchLead) {
      this._globalService.isFilterLeads = false;
      this._globalService.isSearchLead = false;
      this._globalService.leadFilter = {};
      this._globalService.leadIdArray = [];
      this._globalService.currentLeadCount = 0;
      this._globalService.currentModule = 'lead';
    }

    setTimeout(() => {
      if (!this._globalService.userData?.isSubUser) {
        this.firstTimeInfo();
      }
    }, 2000);

    this._sharedService.refreshLeadsGrid = new Subject();
    this._sharedService.refreshLeadsGrid.subscribe((response) => {
      if (response) {
        setTimeout(() => {
          this.refreshLeadsGrid({ type: 'hot' });
        }, 2000);
      }
    });

    this._sharedService.applyQuickFilter = new Subject();
    this._sharedService.applyQuickFilter.subscribe((response) => {
      if (response) {
        setTimeout(() => {
          this.setQuickFilter(response?.data);
          this.masterFilter.getMasterFilterList();
        }, 2000);
      }
    });

    this._route.queryParams.subscribe((query) => {
      if (query?.hotlead) {
        this._router.navigate(['.'], {
          relativeTo: this._route,
          queryParams: {},
        });
        this._globalService.leadFilterName = 'N/A';
        return;
      }
      let { leadType } = query;
      if (!leadType) {
        leadType = '';
      }

      if (this.leadType != leadType) {
        this.saveFilter();
      }

      let type = '';
      this.leads = {};
      if (leadType) {
        this.leadType = leadType;
        this.view = 'LIST';
        this.isViewEnabled = false;
        this.isAddEnabled = false;
        this.isReduceGrid = false;
        type =
          this.leadType == 'warm'
            ? 'warm lead'
            : this.leadType == 'dead'
            ? 'dead lead'
            : this.leadType == 'whiteboard'
            ? 'deals whiteboard'
            : 'referred to agent';
        this.leadIcon =
          type == 'warm lead'
            ? '/assets/images/Warm-lead-1.svg'
            : type == 'dead lead'
            ? '/assets/images/dead-lead-icon-red.svg'
            : type == 'referred to agent'
            ? '/assets/images/Refered-to-agent-1.svg'
            : '/assets/images/leads.svg';
        this.moduleName =
          type == 'referred to agent' || type == 'deals whiteboard'
            ? type
            : type + 's';
        if (type == 'deals whiteboard') {
          this.currentLimit = 1000;
          this.isFilterEnabled = false;
          this.isSearchEnabled = false;
          this.isSortEnabled = false;
          this.isWhiteBoardEnable = true;
          this.isModuleCountEnabled = false;
          this.isExportEnable = false;
          this.isbackToLeadEnable = true;
          this.leadIcon = '';
          this.isSelectBarEnabled = false;
          this.getHeader();
        } else {
          this.currentLimit = MiscellaneousConstant.paginationLimit.STANDARD;
          this.isExportEnable = true;
          this.isSearchEnabled = true;
          this.isFilterEnabled = true;
          this.isSortEnabled = true;
          this.isWhiteBoardEnable = false;
          this.isbackToLeadEnable = false;
          this.isSelectBarEnabled = true;
        }
        this._explainersPopUpService.filterExplainerData(this.moduleName);
      } else {
        this.currentLimit = MiscellaneousConstant.paginationLimit.STANDARD;
        this.leadType = '';
        this.isAddEnabled = true;
        this.isViewEnabled = true;
        this.isReduceGrid = true;
        this.view = 'GRID';
        this.moduleName = 'Leads';
        this.moduleAddName = 'Lead';
        this.isFilterEnabled = true;
        this.isSearchEnabled = true;
        this.isSortEnabled = true;
        this.isWhiteBoardEnable = false;
        this.isModuleCountEnabled = true;
        this.isExportEnable = true;
        this.isbackToLeadEnable = false;
        type = '';
        this.leadIcon = '/assets/images/leads.svg';
        this._explainersPopUpService.filterExplainerData(this.moduleName);
        this.isSelectBarEnabled = true;
      }

      this.getSavedTemplates(type);
    });
  }

  ngOnDestroy() {
    if (this.viewChangeSubscription) {
      this.viewChangeSubscription.unsubscribe();
    }
    // Unsubscribe from all subscriptions
    if (this.viewChangeSubscription) {
      this.viewChangeSubscription.unsubscribe();
    }
    if (this.getLeadSubscribe) {
      this.getLeadSubscribe.unsubscribe();
    }

    // Complete other subjects
    this.mainStatusSubject.complete();
  }

  firstTimeInfo() {
    this._loaderService.start();
    this._userService
      .getUserExperience({})
      .subscribe((response: ResponseModel) => {
        if (!response?.data) {
          this.dialogRef = this._dialog.open(FirstTimeLoginComponent, {
            width: '1020px',
            panelClass: 're-first-time-modal',
            disableClose: true,
            data: {
              returnAddress:
                this._globalService?.userData?.loginData?.returnAddress,
              email: this._globalService?.userData?.email,
              fromName: this._globalService?.userData?.firstName,
            },
          });
          this.dialogRef.afterClosed().subscribe((result) => {
            if (
              this._globalService.userData &&
              !this._globalService.userData?.isTypeformSubmit
            ) {
              this.openTypeFormDialog();
            }
          });
        } else {
          if (
            this._globalService.userData &&
            !this._globalService.userData?.isTypeformSubmit
          ) {
            this.openTypeFormDialog();
          }
          this._sharedService.getDLCForm({}).subscribe(
            (response: ResponseModel) => {
              if (response.statusCode === 200) {
                this._globalService.dlcForm = response?.data;
                this._loaderService.stop();
                if (
                  this._globalService.dlcForm?.numberCount > 0 &&
                  environment.releaseStage == 'live' &&
                  !this._globalService?.userData?.isSubUser &&
                  localStorage.getItem('isDLCOpen') != 'true'
                ) {
                  if (
                    this._globalService.dlcForm?.status == null ||
                    (this._globalService.dlcForm?.status == 'submitted' &&
                      this._globalService.dlcForm?.supportStatus == true)
                  ) {
                    this.dialogRef = this._dialog.open(
                      DlcMessageDialogComponent,
                      {
                        width: '550px',
                      }
                    );
                  }
                } else {
                  this.openOnboardingDialog();
                }
              }
            },
            (err: ErrorModel) => {
              this._loaderService.stop();
            }
          );
        }
      });
  }

  openTypeFormDialog() {
    this.dialogRef = this._dialog.open(TypeformDialogComponent, {
      disableClose: true,
      panelClass: 're-refer-a-friend-modal',
      width: '1020px',
      data: {},
    });
  }

  openOnboardingDialog() {
    if (
      this._globalService?.userData?.loginData?.isTrialPeriod &&
      !this._globalService?.userData?.loginData?.isOnBoardingCallScheduled
    ) {
      this.onboardingDialog();
    }
  }

  onboardingDialog() {
    this.dialogRef = this._dialog.open(OnboardingCallDailogComponent, {
      // disableClose: true,
      panelClass: 're-onboarding-modal',
      width: '1000px',
      data: {},
    });

    this.dialogRef.afterClosed().subscribe((result) => {});
  }

  getMainStatus(type?, bool?) {
    const obj = {
      page: 1,
      limit: this.currentLimit,
      moduleId: this.moduleId,
    };

    this._sharedService.getMainStatus(obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          this.mainStatus = response.data['items'];
          let statusObj = [
            {
              _id: '5feb4221aa810a3849fa551b',
              title: 'sold',
            },
          ];
          if (this.leadType !== 'referredtoagent') {
            this.mainStatus.push({
              _id: '60221e37d8562ef0219c072a',
              title: 'inventory',
            });
          }
          this.mainStatus.push(...statusObj);
          this._globalService.checkForStatusNameChange(
            this.mainStatus,
            'leads'
          );
          let status = [];

          if (this.leadType) {
            status = response.data['items'].filter((x) => x.title === type);
          } else {
            if (this._globalService?.leadFilter?.mainStatus) {
              let filterData = this._globalService?.leadFilter?.mainStatus;
              let filterValue = filterData.value;
              let filterOprator = filterData?.operator;
              const iswarmLead = filterValue.includes(
                StatusConstant.LeadStatus.WARM_LEAD
              );
              const isdeadLead = filterValue.includes(
                StatusConstant.LeadStatus.DEAD_LEAD
              );
              const isreferredToAgent = filterValue.includes(
                StatusConstant.LeadStatus.REFERRED_TO_AGENT
              );

              if (filterOprator == 'is') {
                if (iswarmLead && isdeadLead && isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                  status = status.sort(function (a, b) {
                    if (a.title == 'referred to agent') {
                      a.orderIndex = 9;
                    }
                    if (a.title == 'dead lead') {
                      a.orderIndex = 8;
                    }
                    if (a.title == 'warm lead') {
                      a.orderIndex = 7;
                    }
                    if (b.title == 'referred to agent') {
                      b.orderIndex = 9;
                    }
                    if (b.title == 'dead lead') {
                      b.orderIndex = 8;
                    }
                    if (b.title == 'warm lead') {
                      b.orderIndex = 7;
                    }
                    return a.orderIndex > b.orderIndex
                      ? 1
                      : a.orderIndex < b.orderIndex
                      ? -1
                      : 0;
                  });
                } else if (iswarmLead && isdeadLead) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (iswarmLead && isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'dead lead' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (isdeadLead && isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'warm lead' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (iswarmLead) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'dead lead' &&
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (isdeadLead) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'warm lead' &&
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'dead lead' &&
                      x.title !== 'warm lead' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'warm lead' &&
                      x.title !== 'dead lead' &&
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                }
              } else {
                // Handle 'is not' operator cases with the same conditions
                if (iswarmLead && isdeadLead && isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'warm lead' &&
                      x.title !== 'dead lead' &&
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (iswarmLead && isdeadLead) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'dead lead' &&
                      x.title !== 'warm lead' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (iswarmLead && isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'warm lead' &&
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (isdeadLead && isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'dead lead' &&
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (iswarmLead) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'warm lead' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (isdeadLead) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'dead lead' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else if (isreferredToAgent) {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'referred to agent' &&
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                } else {
                  status = response.data['items'].filter(
                    (x) =>
                      x.title !== 'inventory' &&
                      x.title !== 'sold' &&
                      x.title !== 'rental'
                  );
                }
              }
            } else if (
              this._globalService?.leadFilter?.deadResons &&
              this._globalService?.leadFilter?.deadResons.operator == 'is'
            ) {
              status = response.data['items'].filter(
                (x) =>
                  x.title !== 'warm lead' &&
                  x.title !== 'referred to agent' &&
                  x.title !== 'inventory' &&
                  x.title !== 'sold' &&
                  x.title !== 'rental'
              );
            } else {
              status = response.data['items'].filter(
                (x) =>
                  x.title !== 'warm lead' &&
                  x.title !== 'dead lead' &&
                  x.title !== 'referred to agent' &&
                  x.title !== 'inventory' &&
                  x.title !== 'sold' &&
                  x.title !== 'rental'
              );
            }
          }

          // Single call to getView with all filtered status
          if (status.length > 0) {
            this.getView(status, bool);
          }
        }
      },
      (err: ErrorModel) => {
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
    this.mainStatusSubject.next();
  }

  getView(data, bool?) {
    this.count = 0;
    this.masterFilter.getMasterFilterList();
    if (this.view == 'GRID') {
      this.applySort = {};
      this.isReduceGrid = true;
      this.header = data;
      // GET LEADS BASED ON PROJECT STATUS
      data.filter((x) => {
        this.getLeads([x._id], bool);
      });
    } else {
      this.isReduceGrid = false;
      $('.popover_close').trigger('click');
      if (this.isWhiteBoardEnable) {
        this.mainStatusArray = [
          StatusConstant.LeadStatus.UNDER_CONTRACT,
          StatusConstant.LeadStatus.ASSIGN_TO_BUYER,
        ];
        this.getLeads(this.mainStatusArray, bool);
      } else {
        this.mainStatusId = [];
        // this.header = [
        //   {
        //     label: 'Name',
        //     key: 'title',
        //     isClickExists: true,
        //     event: 'LEAD_DETAILS',
        //     isMultiSelect: true,
        //     sort: { type: this.sortMode },
        //   },
        //   {
        //     label: 'Address',
        //     key: 'address',
        //     isClickExists: true,
        //     event: 'LEAD_DETAILS',
        //     sort: { type: this.sortMode },
        //     pipe: 'ADDRESS_FORMAT',
        //   },
        //   {
        //     label: 'Status',
        //     key: 'mainStatusTitle',
        //     style: { 'text-transform': 'capitalize' },
        //     isClickExists: true,
        //     event: 'LEAD_DETAILS',
        //     sort: { type: this.sortMode },
        //   },
        //   {
        //     label: 'Phone',
        //     key: 'phoneNumber',
        //     isClickExists: true,
        //     event: 'LEAD_DETAILS',
        //     pipe: 'PHONE',
        //     sort: { type: this.sortMode },
        //   },
        //   {
        //     label: 'Email',
        //     key: 'email',
        //     isClickExists: true,
        //     event: 'LEAD_DETAILS',
        //     sort: { type: this.sortMode },
        //   },
        //   {
        //     label: 'Action',
        //     key: 'action',
        //     options: [
        //       {
        //         type: 'DELETE_LEAD',
        //         icon: '/assets/images/delete.svg',
        //         activeIcon: '/assets/images/deleteActive.svg',
        //       },
        //       {
        //         type: 'EDIT',
        //         route: '/leads/edit',
        //         icon: '/assets/images/edit.svg',
        //         activeIcon: '/assets/images/editActive.svg',
        //       },
        //     ],
        //   },
        // ];

        this.getColumns();
        data.filter((x) => {
          this.mainStatusId.push(x._id);
        });
        this.getLeads(this.mainStatusId, bool);
      }
    }
  }

  setView($event) {
    this.view = $event;
    this.currentPage = 1;
    this.leads = {};
    this.debouncedSaveFilter();
    this.getMainStatus('', true);
  }

  setGridView($event) {
    $('.popover_close').parents('.popover').popover('hide');
    $('body .infinite-scroll').removeClass('re-scroll-hide');
    $('body .re-card').removeClass('re-open-card');
    if ($event) {
      $('body').addClass('popover-none');
    } else {
      $('body').removeClass('popover-none');
    }

    this.reduceGrid = $event;
    this._globalService.reduceGrid = $event;
    this.debouncedSaveFilter();
  }

  setSort($event) {
    // this._loaderService.start();
    this._globalService.leadSort = $event;
    this.sort = $event;
    this.currentPage = 1;
    this.leads = {};
    let type = '';
    if (this.leadType) {
      type =
        this.leadType == 'warm'
          ? 'warm lead'
          : this.leadType == 'dead'
          ? 'dead lead'
          : 'referred to agent';
    }
    this.debouncedSaveFilter();
    this.getMainStatus(type, true);
  }

  setFilter($event) {
    if ($event === this.moduleName) {
      let leadFilter = { ...this._globalService.leadFilter };
      this.dialogRef = this._dialog.open(LeadsFilterComponent, {
        width: '980px',
        data: {
          type: 'Filter',
          isFilterLeads: this._globalService.isFilterLeads,
          leadFilter: leadFilter,
          leadType: this.leadType,
        },
      });

      this.dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          if (result?.reset) {
            this._globalService.isFilterLeads = false;
            this._globalService.leadFilter = {};
          } else {
            this._globalService.isFilterLeads = true;
            this._globalService.leadFilter = result;
          }
          this.currentPage = 1;
          this.leads = {};
          let type = '';
          if (this.leadType) {
            type =
              this.leadType == 'warm'
                ? 'warm lead'
                : this.leadType == 'dead'
                ? 'dead lead'
                : 'referred to agent';
          }
          this.saveFilter();
          this.getMainStatus(type, true);
        }
      });
    }
  }

  setQuickFilter($event) {
    let currentDate = this._globalService.dueDateFormat(new Date(), '', {
      hours: 0,
      minutes: 0,
      seconds: 0,
    }).timeStamp;

    if ('buyerContingencies' in $event) {
      $event['buyerContingencies']['currentDate'] = currentDate;
    }

    if ('sellerContingencies' in $event) {
      $event['sellerContingencies']['currentDate'] = currentDate;
    }

    this._globalService.isFilterLeads = true;
    this._globalService.leadFilter = $event;
    this.currentPage = 1;
    this.leads = {};
    let type = '';
    if (this.leadType) {
      type =
        this.leadType == 'warm'
          ? 'warm lead'
          : this.leadType == 'dead'
          ? 'dead lead'
          : 'referred to agent';
    }

    this.saveFilter();
    this.getMainStatus(type, true);
  }

  setSearch($event) {
    if ($event === this.moduleName) {
      this.dialogRef = this._dialog.open(SearchDialogComponent, {
        width: '500px',
        data: {
          header: 'Lead',
        },
      });
      this.dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this._globalService.isFilterLeads = true;
          this._globalService.leadFilter = {
            searchString: result,
          };
          this.leads = {};
          let type = '';
          if (this.leadType) {
            type =
              this.leadType == 'warm'
                ? 'warm lead'
                : this.leadType == 'dead'
                ? 'dead lead'
                : 'referred to agent';
          }
          this.debouncedSaveFilter();
          this.getMainStatus(type, true);
        }
      });
    }
  }

  async resetFilter() {
    this._loaderService.start();
    await this.masterFilter.activeQuickFilter({}, {});
    this.currentPage = 1;
    this._globalService.isFilterLeads = false;
    this._globalService.leadFilter = {};
    this.leads = {};
    let type = '';
    if (this.leadType) {
      type =
        this.leadType == 'warm'
          ? 'warm lead'
          : this.leadType == 'dead'
          ? 'dead lead'
          : 'referred to agent';
    }
    this.masterFilter.activatedFilter = null;

    // update filter template
    let filterData;
    if (this.templates.length > 0) {
      filterData = JSON.parse(this.templates[0]?.filterData);
      filterData?.filter((item) => {
        item.leadFilter = {};
        item.isFilterLeads = false;
        item.leadFilterName = '';
        return;
      });
    }

    let newObj = {
      title: `leads filter _${this.loginUserId}`,
      filterTemplateId: this.templates[0]?._id,
      filterData: JSON.stringify(filterData),
    };

    this._sharedService
      .saveFilterTemplates(newObj, 'edit')
      .subscribe((response: ResponseModel) => {
        if (response?.statusCode == 200) {
          this.templates[0] = response?.data;
        }
      });

    // await this.saveFilter();
    await this.getMainStatus(type, true);
  }

  changePage($event) {
    if (this.selectedItems.length && this.selectActionType !== 'All') {
      this.resetSelectedItems();
    }

    this.currentPage = $event;
    if (this.isWhiteBoardEnable) {
      this.getLeads(this.mainStatusArray);
    } else {
      this.getLeads(this.mainStatusId);
    }
  }

  scrollPage(event) {
    if (!this.isPageScroll[event]) {
      this.isPageScroll[event] = true;
      this.currentPage = this.leads[event].page + 1;
      this.getLeads([event]);
    }
  }

  getLeads(statusId, bool?) {
    if (bool) {
      this.currentPage = 1;
    }

    let obj = {
      mainStatusId: statusId,
      holdingSort: this.sort == 'DESC' ? true : false,
      page: this.currentPage,
      limit: this.currentLimit,
    };
    this._globalService.dialerLeadSort = this.sort == 'DESC' ? true : false;

    if (this.applySort) {
      obj = { ...obj, ...this.applySort };
    }

    if (this._globalService.isFilterLeads) {
      obj['filterData'] = this._globalService.leadFilter;
      obj['isFilter'] = true;
    }

    if (
      this.view === 'LIST' &&
      this.paginationType === MiscellaneousConstant.paginationType.INFINITE
    ) {
      obj['limit'] = MiscellaneousConstant.paginationLimit.INFINITE;
    }

    delete obj['selectedStartDate'];
    delete obj['selectedEndDate'];
    this.isLoading = true;
    // this._loaderService.startLoader(statusId.length == 1 ? statusId[0] : 'ALL');
    this.getLeadSubscribe = this._leadService.getLeadsWithFilter(obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          this.isPageScroll[statusId] = false;

          if (bool) {
            this.count += response?.data?.count || 0;
          }

          if (!response?.data?.items) {
            this.isLoading = false;
            response.data.items = [];
          }

          for (let i = 0; i < response.data.items.length; i++) {
            var item = response.data.items[i];
            this.updateLeadItem(item);
            this.assignUser(item);
          }
          if (this.isWhiteBoardEnable) {
            this.leads = response.data;
            this._loaderService.stopLoader(
              statusId.length == 1 ? statusId[0] : 'ALL'
            );
            this._loaderService.stop();

            return;
          }

          if (this.view === 'LIST') {
            this.isLoading = false;
            if (bool) {
              //new implement for filter start
              if (this._globalService?.leadFilter?.mainStatus) {
                const filterData =
                  this._globalService?.leadFilter?.mainStatus?.value || [];
                const leadTypeMap = {
                  warm: StatusConstant.LeadStatus.WARM_LEAD,
                  dead: StatusConstant.LeadStatus.DEAD_LEAD,
                  referredtoagent: StatusConstant.LeadStatus.REFERRED_TO_AGENT,
                };

                if (
                  this.leadType === '' ||
                  filterData.includes(leadTypeMap[this.leadType])
                ) {
                  this.leads = response.data;
                  this.leads.count = response.data?.count;
                } else {
                  this.leads = {};
                  this.leads.count = 0;
                }
              } else {
                this.leads = response.data;
                this.leads.count = response.data?.count;
              }
              //new implement for filter end

              if (
                this.paginationType ===
                MiscellaneousConstant.paginationType.STANDARD
              ) {
                this.advancedlistView.pagination.count =
                  response.data?.count || 0;
                this.advancedlistView.pagination.pageSize = this.currentLimit;
                this.advancedlistView.pagination.setPage(1, true);
              }
            } else {
              // STANDARD PAGINATION
              if (
                this.paginationType ===
                MiscellaneousConstant.paginationType.STANDARD
              ) {
                this.leads.items = response.data.items;
              }

              // INFINITE PAGINATION
              if (
                this.paginationType ===
                MiscellaneousConstant.paginationType.INFINITE
              ) {
                this.leads.items = this.leads.items
                  ? [...this.leads.items, ...response.data.items]
                  : response.data.items;
              }
            }

            if (this.isSelectBarEnabled && this.selectActionType === 'All') {
              this.leads?.items?.map((x) => (x.isCompleted = true));
            }
          } else {
            if (this.leads[statusId]) {
              if (
                !this.leads[statusId].items.some((item) =>
                  response.data.items.find(
                    (newItem) => newItem._id === item._id
                  )
                )
              ) {
                this.leads[statusId].items = [
                  ...this.leads[statusId].items,
                  ...response.data.items,
                ];
              }
              this.leads[statusId].page = response.data.page;
            } else {
              this.leads[statusId] = response.data;
            }

            //new implement for filter start
            if (this._globalService?.leadFilter?.mainStatus) {
              let statusValue =
                this._globalService?.leadFilter?.mainStatus?.value;
              let statusOperator =
                this._globalService.leadFilter?.mainStatus?.operator;
              if (statusOperator == 'is') {
                if (!statusValue.includes(statusId.toString())) {
                  this.leads[statusId].items = [];
                  this.leads[statusId].count = 0;
                }
              } else {
                if (statusValue.includes(statusId.toString())) {
                  this.leads[statusId].items = [];
                  this.leads[statusId].count = 0;
                }
              }
            }
            //new implement for filter end
          }
        } else {
          this.leads = {};
          this._toastrService.error(response.message, '');
        }

        if (this.gridView) this.gridView.getHeight();
        this._loaderService.stopLoader(
          statusId.length == 1 ? statusId[0] : 'ALL'
        );
        this.isLoading = false;
        this._loaderService.stop();
      },
      (err: ErrorModel) => {
        this.isLoading = false;
        this._loaderService.stop();
        this._loaderService.stopLoader(
          statusId.length == 1 ? statusId[0] : 'ALL'
        );
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  _emitter(event) {
    // Handle delete lead event
    if (event.index > -1 && event.details) {
      this.deleteLead(event.details?._id, event.index);
    }

    // Handle status change event
    if (event.type === 'statusChange' && event.statusId && event.item) {
      this.updateLeadStatusConfirm(event);
    }

    // Handle column configuration update event
    // if (event.type === 'COLUMN_CONFIG_UPDATED' && event.data) {
    //   this.updateColumns(event.data);
    // }
  }

  deleteLead(leadId, index) {
    this._loaderService.start();
    let obj = {
      leadId,
    };

    this._leadService.deleteLead(obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          // this._toastrService.success(this.messageConstant.leadDeletedSuccess);
          this._loaderService.stop();
          this.leads.items.splice(index, 1);
          this.leads.count -= 1;
          let deleteObj = {
            lead_property_id: leadId,
          };
          this._leadService.deleteSingleProperty(deleteObj).subscribe(
            (response: ResponseModel) => {
              if (response.statusCode == 200) {
              }
            },
            (err: ErrorModel) => {
              this._loaderService.stop();
              if (err.error) {
                const error: ResponseModel = err.error;
                this._toastrService.error(error.message, '');
              } else {
                this._toastrService.error(
                  this.messageConstant.unknownError,
                  ''
                );
              }
            }
          );
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }
  assignUser(item) {
    let assignUserData = [];

    // Get selected roles from Team Assigned column configuration
    // Use the current status-specific column config instead of defaultColumnConfig
    const { statusId } = this.getColumnMappings();

    // Find the Team Assigned column in the current status configuration
    const teamAssignedColumn = this.statusColumnConfigs[statusId]?.find(
      (col) => col.label === 'Team Assigned' && col.hasRoles
    );

    // Get visible roles from roles array
    let visibleRoles = [];
    if (teamAssignedColumn?.roles) {
      // Get roleIds from roles array where isVisible is true
      visibleRoles = teamAssignedColumn.roles
        .filter((role) => role.isVisible)
        .map((role) => role.roleId);
    }

    // Check if we have any visible roles
    const hasRoles = visibleRoles.length > 0;

    // Process assigned users if they exist
    if (item?.assignUser) {
      // Store role-to-user mapping for tooltip display
      const roleUserMap = {};

      // Process each role and its assigned user
      Object.keys(item.assignUser).forEach((roleId) => {
        const userId = item.assignUser[roleId];
        // Only include users with visible roles
        if (userId && (!hasRoles || visibleRoles.includes(roleId))) {
          const user = this.mainUserList.find((u) => u._id === userId);
          if (user) {
            // Store user by role for tooltip
            if (!roleUserMap[roleId]) {
              roleUserMap[roleId] = [];
            }

            roleUserMap[roleId].push({
              userId: user._id,
              fullName: `${user?.firstName || ''} ${user?.lastName || ''}`,
              img: user.profileImage,
              roleId: roleId,
            });
          }
        }
      });

      // Get all user IDs without filtering by visibility
      const userIds = Object.keys(item.assignUser)
        .map((key) => item.assignUser[key])
        .filter((id) => id != null);

      // Get unique user IDs
      const uniqueIds = [...new Set(userIds)];

      // Create user data for display
      uniqueIds.forEach((userId) => {
        const user = this.mainUserList.find((u) => u._id === userId);
        if (user) {
          const initials =
            (user?.firstName?.charAt(0) || '') +
            (user?.lastName?.charAt(0) || '');

          // Find all roles this user is assigned to (without filtering by visibility)
          const userRoles = Object.keys(item.assignUser).filter(
            (roleId) => item.assignUser[roleId] === userId
          );

          assignUserData.push({
            img: user.profileImage,
            name: initials.toUpperCase(),
            fullName: `${user?.firstName || ''} ${user?.lastName || ''}`,
            roleIds: userRoles, // Use the actual assigned roles, not user.roleId
            isVisible: userRoles.some((roleId) =>
              visibleRoles.includes(roleId)
            ), // Add visibility flag
          });
        }
      });

      // Store the role-user mapping for tooltip display
      item.roleUserMap = roleUserMap;
    }

    item.assignUserData = assignUserData;
  }

  changeSort(obj) {
    const param = {
      sortByy: obj.sortBy,
      sortMode: obj.sortMode,
    };
    this.applySort = param;
    this.saveFilter();
    this.currentPage = 1;
    if (this.isWhiteBoardEnable) {
      this.getLeads(this.mainStatusArray, true);
      let mainStatusArray = [
        '5feb3f94aa810a3849fa5515',
        '5feb3fabaa810a3849fa5516',
        '605ac5fb0eee4f051cee4b02',
        '5feb4173aa810a3849fa5519',
        '5feb41e6aa810a3849fa551a',
      ];
      this.getInventories(mainStatusArray, true);
    } else {
      this.getLeads(this.mainStatusId, true);
    }
  }

  leadDetailsEmitter(item) {
    this._globalService.leadIdArray = [];
    this._globalService.currentLeadCount = 0;
    this._globalService.currentModule = 'lead';

    localStorage.removeItem('RE-TAB');
    if (item.bool) {
      window.open(`/leads/details?leadsId=${item?._id}`);
      return;
    }
    this._router.navigate([`/leads/details`], {
      queryParams: { ['leadsId']: item._id },
    });

    // var statusIds = [item.mainStatusId];
    // if (this.view == 'LIST') {
    //   statusIds = this.mainStatusId;
    // }

    // let obj = {
    //   mainStatusId: statusIds,
    //   holdingSort: this.sort == 'DESC' ? true : false,
    // };
    // if (this.applySort) {
    //   obj = { ...obj, ...this.applySort };
    // }
    // if (this._globalService.isFilterLeads) {
    //   obj = { ...obj, ...this._globalService.leadFilter };
    // }

    // delete obj['selectedStartDate'];
    // delete obj['selectedEndDate'];

    // this._loaderService.start();
    // this._leadService.getListLeadId(obj).subscribe(
    //   (response: ResponseModel) => {
    //     this._loaderService.stop();

    //     if (response?.statusCode && response?.statusCode == 200) {
    //       this._globalService.currentModule = 'lead';
    //       this._globalService.leadIdArray = response?.data;
    //       this._globalService.currentLeadCount =
    //         this._globalService.leadIdArray.indexOf(item._id);

    //       localStorage.removeItem('RE-TAB');
    //       if (item.bool) {
    //         window.open(`/leads/details?leadsId=${item?._id}`);
    //         return;
    //       }
    //       this._router.navigate([`/leads/details`], {
    //         queryParams: { ['leadsId']: item._id },
    //       });
    //     }
    //   },
    //   (err: ErrorModel) => {
    //     this._loaderService.stop();
    //     if (err.error) {
    //       const error: ResponseModel = err.error;
    //       this._toastrService.error(error.message, '');
    //     } else {
    //       this._toastrService.error(this.messageConstant.unknownError, '');
    //     }
    //   }
    // );
  }

  updateLeadItem(item) {
    let displayArray = [];
    let pipline = this.pipline(item?.leadCreated); //|| '1'

    if (
      item.mainStatusId == StatusConstant.LeadStatus.NO_CONTACT_MADE ||
      item.mainStatusId == StatusConstant.LeadStatus.NEW_LEAD ||
      item.mainStatusId == StatusConstant.LeadStatus.CONTACT_MADE ||
      item.mainStatusId == StatusConstant.LeadStatus.APPOINTMENT_SET ||
      item.mainStatusId == StatusConstant.LeadStatus.DUE_DILIGENCE
    ) {
      let dic = {
        title: 'Created',
        value: item?.leadCreated,
        pipe: 'DATE',
        isDisplayNone: false,
      };
      displayArray.push(dic);
      let dic1 = {
        title: 'In pipeline',
        value: pipline + ' days',
        isDisplayNone: false,
      };
      displayArray.push(dic1);
    }
    // else if (item.mainStatusId == StatusConstant.LeadStatus.CONTACT_MADE) {
    //   let dic = {
    //     title: 'Created',
    //     value: item?.leadCreated,
    //     pipe: 'DATE',
    //     isDisplayNone: false,
    //   };
    //   displayArray.push(dic);
    //   let dic1 = {
    //     title: 'In pipeline',
    //     value: pipline + ' days',
    //     isDisplayNone: false,
    //   };
    //   displayArray.push(dic1);
    // } else if (item.mainStatusId == StatusConstant.LeadStatus.APPOINTMENT_SET) {
    //   let dic = {
    //     title: 'Apt. Date',
    //     value: item?.appointmentDate,
    //     pipe: 'DATE_TIME',
    //     isDisplayNone: false,
    //   };
    //   displayArray.push(dic);
    //   let dic1 = {
    //     title: 'In pipeline',
    //     value: pipline + ' days',
    //     isDisplayNone: false,
    //   };
    //   displayArray.push(dic1);
    // }
    else if (item.mainStatusId == StatusConstant.LeadStatus.OFFER_MADE) {
      let dic = {
        title: 'Offer Date',
        value: item?.dateOfYourOffer,
        pipe: 'DATE',
        isDisplayNone: false,
      };
      displayArray.push(dic);
      let dic1 = {
        title: 'Offer Price',
        value: item?.yourOfferPrice,
        pipe: 'CURRENCY',
        isDisplayNone: false,
      };
      displayArray.push(dic1);
      let dic2 = {
        title: 'In pipeline',
        value: pipline + ' days',
        isDisplayNone: true,
      };
      displayArray.push(dic2);
    } else if (item.mainStatusId == StatusConstant.LeadStatus.UNDER_CONTRACT) {
      let dic = {
        title: 'Schd. closing',
        value: item?.closingDate,
        pipe: 'DATE',
        isDisplayNone: false,
      };
      displayArray.push(dic);
      let dic1 = {
        title: 'U.C price',
        value: item?.underContractPrice,
        pipe: 'CURRENCY',
        isDisplayNone: false,
      };
      displayArray.push(dic1);
      let dic2 = {
        title: 'U.C date',
        value: item?.underContractDate,
        pipe: 'DATE',
        isDisplayNone: true,
      };
      displayArray.push(dic2);
      let dic3 = {
        title: 'In pipeline',
        value: pipline + ' days',
        isDisplayNone: true,
      };
      displayArray.push(dic3);
    } else if (item.mainStatusId == StatusConstant.LeadStatus.ASSIGN_TO_BUYER) {
      let dic = {
        title: 'Schd. closing',
        value: item?.closingDate,
        pipe: 'DATE',
        isDisplayNone: false,
      };
      displayArray.push(dic);
      let dic1 = {
        title: 'Assig. fee',
        value: item?.assignmentFee,
        pipe: 'CURRENCY',
        isDisplayNone: false,
      };
      displayArray.push(dic1);
      let dic2 = {
        title: 'Assig. date',
        value: item?.assignmentContractDate,
        pipe: 'DATE',
        isDisplayNone: true,
      };
      displayArray.push(dic2);
      let dic3 = {
        title: 'In pipeline',
        value: pipline + ' days',
        isDisplayNone: true,
      };
      displayArray.push(dic3);
    } else if (item.mainStatusId == StatusConstant.LeadStatus.DEAD_LEAD) {
      let dic = {
        title: 'Created',
        value: item?.leadCreated,
        pipe: 'DATE',
        isDisplayNone: false,
      };
      displayArray.push(dic);
      let dic1 = {
        title: 'In pipeline',
        value: pipline + ' days',
        isDisplayNone: false,
      };
      displayArray.push(dic1);
    } else if (item.mainStatusId == StatusConstant.LeadStatus.WARM_LEAD) {
      let dic = {
        title: 'Created',
        value: item?.leadCreated,
        pipe: 'DATE',
        isDisplayNone: false,
      };
      displayArray.push(dic);
      let dic1 = {
        title: 'In pipeline',
        value: pipline + ' days',
        isDisplayNone: false,
      };
      displayArray.push(dic1);
    } else if (
      item.mainStatusId == StatusConstant.LeadStatus.REFERRED_TO_AGENT
    ) {
    }
    item.displayArray = displayArray;
  }

  getDate(item) {
    try {
      if (item?.isUpdated) {
        if (item?.updatedAt) {
          return new Date(item?.updatedAt);
        }

        if (item?.leadId?.updatedAt) {
          return new Date(item?.leadId?.updatedAt);
        }
      } else {
        if (item?.createdAt) {
          return new Date(item?.createdAt);
        } else if (item?.leadId?.createdAt) {
          return new Date(item?.leadId?.createdAt);
        }
      }
    } catch (error) {}
  }

  pipline(first) {
    const nowDate = new Date().getTime();
    const createdDate = new Date(first).getTime();
    const differenceInTime = nowDate - createdDate;
    let days = Math.round(differenceInTime / (1000 * 3600 * 24));
    return days > 0 ? days : 0;
  }

  openExportLeadConfirm() {
    this.dialogRef = this._dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        yesTitle: 'Yes',
        noTitle: 'Cancel',
        header: 'Export Leads',
        text: 'Are you sure you want to export current leads?',
      },
    });

    this.dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.exportLead();
      }
    });
  }

  exportLead() {
    let obj = {
      holdingSort: this.sort == 'DESC' ? true : false,
      leadType: this.leadType,
    };
    if (this.applySort) {
      obj = { ...obj, ...this.applySort };
    }
    // if (this._globalService.isFilterLeads) {
    //   obj = { ...obj, ...this._globalService.leadFilter };
    // }

    if (this._globalService.isFilterLeads) {
      obj['filterData'] = this._globalService.leadFilter;
      obj['isFilter'] = true;
    }

    if (this.isExportList) {
      let ids = [];

      if (this.selectActionType != 'All') {
        this.selectedItems.filter((x) => {
          ids.push(x?._id);
        });
      }

      obj['leadIds'] = ids;
      if (this.selectActionType == 'Custom') {
        obj['skip'] = this.selectedFrom;
        obj['limit'] = this.selectedTo;
      }
    }

    delete obj['selectedStartDate'];
    delete obj['selectedEndDate'];
    let roleNames = MiscellaneousConstant.roleNames;
    this._loaderService.start('loader-01');
    this._leadService.getExportLeads(obj).subscribe(
      (response: ResponseModel) => {
        this._loaderService.stop('loader-01');
        if (response.statusCode && response.statusCode == 200) {
          this.listsDataCSV = response?.data;

          let fieldSequence = [
            {
              id: '_id',
              label: 'Property ID',
            },
            {
              id: 'firstName',
              label: 'First Name',
            },
            {
              id: 'lastName',
              label: 'Last Name',
            },
            {
              id: 'phoneNumber',
              label: 'Phone Number',
            },
            {
              id: 'email',
              label: 'Email Address',
            },
            {
              id: 'mainStatusTitle',
              label: 'Lead Status',
            },
            {
              id: 'crmQuestionTitle',
              label: 'Lead Source',
            },
            {
              id: 'marketingTitle',
              label: 'Campaign Name',
            },
            {
              id: 'streetAddress',
              label: 'Property Street Address',
            },
            {
              id: 'unitNo',
              label: 'Property Street Address 2',
            },
            {
              id: 'city',
              label: 'Property City',
            },
            {
              id: 'state',
              label: 'Property State',
            },
            {
              id: 'zip',
              label: 'Property Zip',
            },
            {
              id: 'ownerMailingAddress',
              label: 'Mailing Address',
            },
            {
              id: 'bedroomsCount',
              label: 'Bedroom',
            },
            {
              id: 'bathCount',
              label: 'Bathroom',
            },
            {
              id: 'area',
              label: 'Apporx Sqft',
            },
            {
              id: 'lotSizeSqft',
              label: 'Lot Size Sqft',
            },
            {
              id: 'yearBuilt',
              label: 'Year Buit',
            },
            {
              id: 'useCode',
              label: 'House Type',
            },
            {
              id: 'mortgageAmount',
              label: 'Mortgage Amount',
            },
            {
              id: 'mortgageDate',
              label: 'Mortgage Date',
            },
            {
              id: 'taxAssessedYear',
              label: 'Tax Assessed Year',
            },
            {
              id: 'taxAssessedValue',
              label: 'Tax Assessed Value',
            },
            {
              id: 'taxBilledAmount',
              label: 'Tax Billed Amount',
            },
            // {
            //   id: 'lastSoldDate',
            //   label: 'Last Sold Date',
            // },
            {
              id: 'lastSoldPrice',
              label: 'Last Sold Price',
            },
            // {
            //   id: 'priorSaleDate',
            //   label: 'Prior Sale Date',
            // },
            {
              id: 'priorSalePrice',
              label: 'Prior Sale Price',
            },
            {
              id: 'garageStatus',
              label: 'Is There a Garage?',
            },
            {
              id: 'garageSqft',
              label: 'Garage Size',
            },
            {
              id: 'garageType',
              label: 'Garage attached or detached',
            },
            {
              id: 'leadCreated',
              label: 'Lead Created Date',
            },
            {
              id: 'appointmentDate',
              label: 'Appointment Date',
            },
            {
              id: 'yourOfferPrice',
              label: 'Offer Price',
            },
            {
              id: 'dateOfYourOffer',
              label: 'Offer Date',
            },
            {
              id: 'underContractDate',
              label: 'Under Contract Date',
            },
            {
              id: 'underContractPrice',
              label: 'Under Contract Price',
            },
            {
              id: 'closingDate',
              label: 'Schedule Closing Date',
            },
            {
              id: 'assignmentFee',
              label: 'Expected Profit',
            },
            {
              id: 'assignmentContractDate',
              label: 'Assignment Contract Date',
            },
            {
              id: 'buyerName',
              label: 'Buyer Name',
            },
            {
              id: 'buyerPhoneNumber',
              label: 'Buyer Phone Number',
            },
            {
              id: 'buyerEmail',
              label: 'Buyer Email',
            },
            {
              id: 'tags',
              label: 'Tags',
            },
            {
              id: 'deadReasonsData',
              label: 'Dead Lead Reason',
            },
          ];
          Object.keys(roleNames).map((key) => {
            fieldSequence.push({
              id: key,
              label: roleNames[key],
            });
          });
          let finalHeader = [];
          let objectOrder = {};
          fieldSequence.forEach((value, index) => {
            finalHeader.push(value.label);
            objectOrder[value.id] = null;
          });
          let rearrangedData = [];
          let keys = [];
          keys = Object.keys(objectOrder);
          let tempObj = {};

          let preferenceKeys = [];
          let secondaryContactKeys = [];
          try {
            for (let i = 0; i < this.listsDataCSV.length; i++) {
              tempObj = {};

              for (let j = 0; j < keys.length; j++) {
                let dataVal = this.listsDataCSV[i][keys[j]];
                if (keys[j] != 'address') {
                  if (
                    typeof dataVal === 'string' ||
                    dataVal instanceof String
                  ) {
                    let dataVal1 = dataVal?.replace(/[,]/g, ';');
                    dataVal = dataVal1;
                  }
                }
                if (keys[j] == 'phoneNumber') {
                  dataVal = this._globalService.maskNumber(dataVal);
                }
                if (
                  dataVal == 0 ||
                  dataVal == '0' ||
                  dataVal == '$' ||
                  dataVal == '$0'
                ) {
                  dataVal = '';
                }
                if (keys[j] == 'firstName' || keys[j] == 'lastName') {
                  let dataVal = this.listsDataCSV[i]['title'];
                  let titleArray = dataVal?.split(' ');
                  let firstName = '';
                  let lastName = '';
                  if (titleArray?.length > 0) {
                    firstName = titleArray[0];
                  }
                  if (titleArray?.length > 1) {
                    lastName = titleArray[1];
                  }
                  tempObj['firstName'] = firstName;
                  tempObj['lastName'] = lastName;
                } else if (
                  keys[j] == 'streetAddress' ||
                  keys[j] == 'city' ||
                  keys[j] == 'state' ||
                  keys[j] == 'zip'
                ) {
                  let address = this.listsDataCSV[i]['address'];
                  let mainAddress = address?.split(',');
                  let streetAddress = '';
                  let city = '';
                  let state = '';
                  let zip = '';
                  if (mainAddress.length > 0) {
                    streetAddress = mainAddress[0] ? mainAddress[0] : '';
                    city = mainAddress.length > 1 ? mainAddress[1] : '';
                    if (mainAddress.length > 2) {
                      let subDivide = mainAddress[2];
                      // if (subDivide) {
                      //   let sub = subDivide.split(' ');
                      //   state = sub[1] ? sub[1] : '';
                      //   zip = sub[2] ? sub[2] : '';
                      // }
                      if (subDivide) {
                        let sub = subDivide.split(' ');
                        state = sub[1] ? sub[1] : '';
                      }

                      let splitAddress = mainAddress[mainAddress?.length - 1];
                      let splitZip = splitAddress.split(' ');
                      zip = splitZip[splitZip?.length - 1];
                    }
                  }

                  tempObj['streetAddress'] = streetAddress;
                  tempObj['city'] = city;
                  tempObj['state'] = state;
                  tempObj['zip'] = zip;
                } else if (keys[j] == 'tags') {
                  if (dataVal?.length > 0) {
                    tempObj[keys[j]] = dataVal?.join(', ');
                  } else {
                    tempObj[keys[j]] = '';
                  }
                } else if (keys[j] == 'deadReasonsData') {
                  if (dataVal?.length > 0) {
                    tempObj[keys[j]] = dataVal?.join(', ');
                  } else {
                    tempObj[keys[j]] = '';
                  }
                } else if (keys[j] == 'secondaryContact') {
                  if (dataVal?.length > 0) {
                    tempObj[keys[j]] = dataVal?.join(', ');
                  } else {
                    tempObj[keys[j]] = '';
                  }
                } else {
                  tempObj[keys[j]] = dataVal;
                }
              }
              for (
                let k = 0;
                k < this.listsDataCSV[i]?.secondaryContact?.length;
                k++
              ) {
                let secondaryContactDic =
                  this.listsDataCSV[i]?.secondaryContact[k];
                let key1 = 'secondaryContactName' + (k + 1);
                let key2 = 'secondaryContact' + (k + 1);
                if (finalHeader.indexOf(key1) == -1) {
                  finalHeader.push(key1);
                  secondaryContactKeys.push(key1);
                }
                if (finalHeader.indexOf(key2) == -1) {
                  finalHeader.push(key2);
                  secondaryContactKeys.push(key2);
                }
                tempObj[key1] = secondaryContactDic?.name;
                tempObj[key2] = secondaryContactDic?.phoneNumber;
              }
              for (
                let k = 0;
                k < this.listsDataCSV[i]?.preference?.length;
                k++
              ) {
                let preferenceDic = this.listsDataCSV[i]?.preference[k];
                for (let l = 0; l < preferenceDic?.question?.length; l++) {
                  let questionDic = preferenceDic?.question[l];

                  let tempKey = questionDic?.questionTitle;
                  let tempKey1 = tempKey?.replace(/[^a-zA-Z0-9 ]/g, '');
                  let tempKey2 = tempKey1?.replace(/[ ]/g, '_');
                  let questionTitle = questionDic?.questionTitle?.replace(
                    /[,]/g,
                    ';'
                  );
                  if (finalHeader.indexOf(questionTitle) == -1) {
                    finalHeader.push(questionTitle);
                    preferenceKeys.push(tempKey2);
                  }
                  let answer = questionDic?.answer;

                  if (typeof answer === 'string' || answer instanceof String) {
                    let answer1 = answer?.replace(/[,]/g, ';');
                    answer = answer1;
                  }
                  if (questionDic?.questionType == 'CURRENCY') {
                    if (answer == undefined || answer == null || answer == '') {
                      answer = '';
                    } else {
                      answer = '$' + answer;
                    }
                  }
                  if (
                    questionDic?.questionTitle == 'is house vacant or occupied?'
                  ) {
                    if (answer == 'yes') {
                      answer = 'vacant';
                    }
                    if (answer == 'no') {
                      answer = 'occupied';
                    }
                  }
                  tempObj[tempKey2] = this.capitalize(answer);
                }
              }
              let assignUser = this.listsDataCSV[i]?.assignUser;
              if (Object.keys(assignUser)?.length > 0) {
                Object.keys(assignUser).map((key) => {
                  if (assignUser[key] != '') {
                    tempObj[key] = this.getAssignUserName(assignUser[key]);
                  }
                });
              }
              rearrangedData.push(tempObj);
            }
          } catch (error1) {
            console.log('error1 = ', error1);
          }

          keys = [...keys, ...preferenceKeys, ...secondaryContactKeys];
          const replacer = (key, value) => (value === null ? '' : value); // specify how you want to handle null values here
          let csv = rearrangedData.map((row) =>
            keys
              .map((fieldName) => JSON.stringify(row[fieldName], replacer))
              .join(',')
          );
          csv.unshift(finalHeader.join(','));
          let csvArray = csv.join('\r\n');

          var blob = new Blob([csvArray], { type: 'text/csv' });

          let d = new Date();
          let n = d.toLocaleTimeString();
          n = n.replace(/[:]/g, '');
          let exportFileName = 'Exported_Leads_' + n + '.csv';
          saveAs(blob, exportFileName);
          this.refreshList(true);
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop('loader-01');
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  capitalize(s) {
    if (s && s != '' && s?.length > 0) {
      return s && s[0].toUpperCase() + s.slice(1);
    }
    return '';
  }

  refreshLeadsGrid(event) {
    this.currentPage = 1;
    this.leads = {};
    let type = '';
    if (this.leadType) {
      type =
        this.leadType == 'warm'
          ? 'warm lead'
          : this.leadType == 'dead'
          ? 'dead lead'
          : 'referred to agent';
    }
    this.getMainStatus(type, true);
  }

  getUser() {
    let param = {};
    this._loaderService.start();
    this._userService.getUsers(param).subscribe(
      (response: ResponseModel) => {
        this._loaderService.stop();
        if (response.statusCode == 200) {
          this.mainUserList = response?.data;
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  /**
   * FUNCTION: UNDER MAINTENANCE COMPONENT LOAD
   * @param {Boolean} bool
   * @returns UNDER MAINTENANCE COMPONENT VIEW
   */
  underMaintenanceView(bool) {
    this.isUnderMaintenance = bool;
  }
  getAssignUserName(userId) {
    let fullName = '';
    this.mainUserList.forEach((elem2, index) => {
      elem2;
      if (userId === elem2._id) {
        let name;
        let lName;
        name = elem2?.firstName
          ? this._globalService.capitalizeName(elem2?.firstName) + ' '
          : '';
        lName = elem2?.lastName
          ? this._globalService.capitalizeName(elem2?.lastName)
          : '';
        fullName = name + lName;
      }
    });
    return fullName;
  }

  async saveFilter() {
    let obj = {},
      newObj = {},
      arrObj = [],
      filterData,
      isItemMatch = false;
    if (this.templates.length > 0) {
      filterData = JSON.parse(this.templates[0]?.filterData);
      await filterData?.filter((item) => {
        if (item.leadType == this.leadType) {
          item.sort = this.sort;
          item.view = this.view;
          item.reduceGrid = this.reduceGrid;
          item.applySort = this.applySort;
          item.leadFilter = this._globalService.leadFilter;
          item.isFilterLeads = this._globalService.isFilterLeads;
          item.leadType = this.leadType;
          isItemMatch = true;
          item.leadFilterName = this._globalService.leadFilterName;
          return;
        }
      });
      if (isItemMatch == false) {
        obj = {
          sort: this.sort,
          view: this.view,
          reduceGrid: this.reduceGrid,
          applySort: this.applySort,
          leadFilter: this._globalService.leadFilter,
          isFilterLeads: this._globalService.isFilterLeads,
          leadType: this.leadType,
          leadFilterName: this._globalService.leadFilterName,
        };
        filterData?.push(obj);
      }
    } else {
      arrObj = [
        {
          sort: this.sort,
          view: this.view,
          reduceGrid: this.reduceGrid,
          applySort: this.applySort,
          leadFilter: this._globalService.leadFilter,
          isFilterLeads: this._globalService.isFilterLeads,
          leadFilterName: this._globalService.leadFilterName,
          leadType: this.leadType,
        },
      ];
    }

    if (this.templates.length > 0) {
      newObj = {
        filterTemplateId: this.templates[0]?._id,
        filterData: JSON.stringify(filterData),
      };
    } else {
      newObj = {
        title: `leads filter _${this.loginUserId}`,
        moduleId: this.moduleId,
        filterData: JSON.stringify(arrObj),
      };
    }
    this._sharedService
      .saveFilterTemplates(newObj, this.templates.length > 0 ? 'edit' : 'save')
      .subscribe(
        (response: ResponseModel) => {
          if (response?.statusCode == 200) {
            this.templates[0] = response?.data;
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
        }
      );
  }

  getSavedTemplates(type = '') {
    if (this.moduleId == '') {
      return;
    }
    const obj = {
      moduleId: this.moduleId,
      page: this.currentPage,
      limit: 100,
    };

    this._sharedService
      .getFilterTemplates(obj)
      .subscribe((response: ResponseModel) => {
        if (response?.statusCode == 200) {
          this.templates = [...response.data?.items];
          if (this.templates?.length && !this.isWhiteBoardEnable) {
            const filterData = JSON.parse(this.templates[0]?.filterData);
            filterData?.filter((item) => {
              if (item?.leadType == this.leadType) {
                if (item?.sort) {
                  this.sort = item?.sort;
                  this.topBar?.sort?.filter((item1) => {
                    if (item1.name == this.sort) {
                      item1.isActive = true;
                    } else {
                      item1.isActive = false;
                    }
                  });
                }
                if (item?.view) {
                  this.view = item?.view;
                  this.topBar?.view?.filter((item2) => {
                    if (item2.name == this.view) {
                      item2.isActive = true;
                    } else {
                      item2.isActive = false;
                    }
                  });
                  this.topBar.isList = this.view == 'LIST' ? true : false;
                }
                if (item?.reduceGrid) {
                  this.reduceGrid = item?.reduceGrid;
                  this._globalService.reduceGrid = this.reduceGrid;
                }
                this.applySort = item?.applySort;
                this._globalService.leadFilter =
                  this._globalService.leadFilter &&
                  Object.keys(this._globalService.leadFilter).length > 0
                    ? this._globalService.leadFilter
                    : item?.leadFilter;
                this._globalService.isFilterLeads = this._globalService
                  .isFilterLeads
                  ? this._globalService.isFilterLeads
                  : item?.isFilterLeads;
                this._globalService.leadFilterName = this._globalService
                  .leadFilterName
                  ? this._globalService.leadFilterName
                  : item?.leadFilterName;
              }
            });
          }
          setTimeout(() => {
            this.getUser();
            this.getMainStatus(type, true);
          }, 500);
        }
      });
  }

  debounce(func, delay) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  }

  emitSelected({ event: type, selectedItems, isAdd, inputType }) {
    if (
      (!selectedItems || !selectedItems?.length) &&
      this.selectActionType != 'Custom'
    ) {
      this._toastrService.error(
        this.messageConstant.selectContactToAction.replace(
          '[[TYPE]]',
          `${type?.toLowerCase()}`
        )
      );
      return;
    }

    // ADD TO DIALER
    if (type === 'addToDialer') {
      let onlySelectedIds = selectedItems.map((x) => x._id);
      if (this._globalService.isDialogOpen) {
        if (this._globalService.dialogcurrentLeadId == selectedItems[0]?._id) {
          this._sharedService.refreshDialerToggleDisplay.next(true);
          return false;
        } else {
          this._toastrService.warning(this.messageConstant.callUnderProgress);
          this._loaderService.stop();
          return false;
        }
      }
      this._globalService.isDialogOpen = true;
      this._globalService.dialogcurrentLeadId = selectedItems[0]?._id;
      this.dialogRef = this._dialog.open(LeadDialerComponent, {
        width: '1366px',
        disableClose: true,
        closeOnNavigation: false,
        backdropClass: 'popup-Backdrop-Class-dialer',
        data: {
          leadId: selectedItems[0]?._id,
          isHideNext: false,
          index: 0,
          fromList: true,
          selectedItems: onlySelectedIds,
          selectActionType: this.selectActionType,
        },
      });
      this.dialogRef.afterClosed().subscribe((result) => {
        this._globalService.isDialogOpen = false;
        if (result?.isEdit) {
        }
      });
    }

    // EXPORT LEADS
    if (type === 'EXPORT') {
      this.isExportList = true;
      this.openExportLeadConfirm();
    }

    // BULK DELETE
    if (type === 'DELETE') {
      let text = `Are you sure you want to delete selected leads`;
      let obj = {
        updateType: this.updateType[type],
        leadIds: selectedItems,
      };
      this.dialogRef = this._dialog.open(ConfirmationDialogComponent, {
        width: '450px',
        data: {
          header: 'Confirmation',
          type: 'Buyers',
          yesTitle: 'Yes',
          text,
        },
      });

      this.dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.updateSelected(obj);
        } else {
          this.refreshList(true);
        }
      });
    }

    // UPDATE TAGS
    if (type === 'TAGS') {
      let obj = {
        type,
        isAdd,
        inputType,
        leadIds: this.selectedItems,
        updateType: this.updateType[type],
        holdingSort: this.sort == 'DESC' ? true : false,
        leadType: this.leadType,
      };

      if (this.applySort) {
        obj = { ...obj, ...this.applySort };
      }
      if (this._globalService.isFilterLeads) {
        obj = { ...obj, ...this._globalService.leadFilter };
      }

      let ids = [];

      if (this.selectActionType != 'All') {
        this.selectedItems.filter((x) => {
          ids.push(x?._id);
        });
      }

      obj['leadIds'] = ids;
      if (this.selectActionType == 'Custom') {
        obj['skip'] = this.selectedFrom;
        obj['limit'] = this.selectedTo;
      }

      this.dialogRef = this._dialog.open(UpdateMultipleDialogComponent, {
        width: '450px',
        data: {
          ...obj,
        },
      });
      this.dialogRef.afterClosed().subscribe((list) => {
        if (list) {
          this.updateSelected({
            updateType: this.updateType[type],
            isAdd,
            list,
            leadIds: selectedItems,
          });
        } else {
          this.refreshList(true);
        }
      });
    }
  }

  selectLeadsAction(obj) {
    this.selectActionType = obj?.mode;
    this.leads['items'].map((x) => (x.isCompleted = false));
    if (obj?.mode == 'All' || obj?.mode == 'Self') {
      this.leads['items'].map((x) => (x.isCompleted = true));
      this.selectedItems = this.leads['items'].filter((x) => x.isCompleted);
      this.toggleActionBar = true;
    }
    // CUSTOM ACTION

    // if (obj?.mode == 'Custom') {
    //   this.selectedFrom = obj.from - 1;
    //   this.selectedTo = obj.to - obj.from + 1;
    //   this.selectedItems = [];
    //   this.toggleActionBar = true;
    // }
  }

  selectItems($event) {
    this.selectedItems = $event;
    this.toggleActionBar = $event?.length ? true : false;
  }

  refreshList($event?) {
    this.selectActionType = '';
    this.selectedItems = [];
    this.toggleActionBar = false;
    this.advancedlistView.reset(this.moduleName);
    this.leads?.items?.map((x) => (x.isCompleted = false));
    if (this.topBar) this.topBar.clearCustomSelection(true);
  }

  resetSelectedItems() {
    this.leads?.items?.map((x) => (x.isCompleted = false));
    this.selectActionType = '';

    if (this.topBar) {
      this.selectedItems = [];
      this.toggleActionBar = false;
      this.topBar.selectedValue = null;
      this.topBar.selectItemsCount = 0;
      this.topBar.selectedTo = 1;
      this.topBar.selectedFrom = this.topBar.totalRecord;
    }
  }

  updateSelected({ updateType, list = [], isAdd = false, leadIds }: any) {
    let obj = {
      holdingSort: this.sort == 'DESC' ? true : false,
      leadType: this.leadType,
      updateType,
      actionType: this.selectActionType,
      isAdd,
    };
    if (this.applySort) {
      obj = { ...obj, ...this.applySort };
    }
    // if (this._globalService.isFilterLeads) {
    //   obj = { ...obj, ...this._globalService.leadFilter };
    // }

    if (this._globalService.isFilterLeads) {
      obj['filterData'] = this._globalService.leadFilter;
      obj['isFilter'] = true;
    }

    let ids = [];
    if (this.selectActionType != 'All') {
      leadIds.filter((x) => {
        ids.push(x?._id);
      });
    }
    obj['leadIds'] = ids;
    if (this.selectActionType == 'Custom') {
      obj['skip'] = this.selectedFrom;
      obj['limit'] = this.selectedTo;
    }

    if (list.length) {
      obj['list'] = list.map((x) => x);
    }

    this._loaderService.start();

    this._leadService.updateLeads(obj).subscribe(
      (response: ResponseModel) => {
        this._loaderService.stop();
        if (response.statusCode === 200) {
          if (updateType == 1) {
            leadIds.filter((x) => {
              let deleteObj = {
                lead_property_id: x?._id,
              };
              this._leadService.deleteSingleProperty(deleteObj).subscribe(
                (response: ResponseModel) => {
                  if (response.code == 200) {
                  }
                },
                (err: ErrorModel) => {
                  this._loaderService.stop();
                  if (err.error) {
                    const error: ResponseModel = err.error;
                    this._toastrService.error(error.message, '');
                  } else {
                    this._toastrService.error(
                      this.messageConstant.unknownError,
                      ''
                    );
                  }
                }
              );
            });
          }
          this.selectActionType = '';
          this.selectedItems = [];
          this.toggleActionBar = false;
          this.leads?.items?.map((x) => (x.isCompleted = false));
          if (this.topBar) this.topBar.clearCustomSelection(true);
          let type = '';
          if (this.leadType) {
            type =
              this.leadType == 'warm'
                ? 'warm lead'
                : this.leadType == 'dead'
                ? 'dead lead'
                : 'referred to agent';
          }
          this.getMainStatus(type, true);
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  whiteBoardListGroup(event: any) {
    if (event == 'all') {
      this.isMonthlyGroup = false;
    } else {
      this.isMonthlyGroup = true;
    }
    this.isWhiteboardListBy = event;
  }

  getHeader(event?: MatTabChangeEvent) {
    let index = event?.index || 0;
    if (index == 0) {
      let mainStatusArray = [
        '5fc7e2777b3b341134e771b6',
        '5fc7e2867b3b341134e771b7',
      ];
      this.leads = {};
      this.waitForMainUserListAndGetLeads(mainStatusArray);
    }
    if (index == 1) {
      let mainStatusArray = [
        '5feb3f94aa810a3849fa5515',
        '5feb3fabaa810a3849fa5516',
        '605ac5fb0eee4f051cee4b02',
        '5feb4173aa810a3849fa5519',
        '5feb41e6aa810a3849fa551a',
      ];
      this.getInventories(mainStatusArray);
    }
  }

  waitForMainUserListAndGetLeads(mainStatusArray: any[]) {
    let checkCount = 0;
    const maxChecks = 30;

    const checkInterval = setInterval(() => {
      checkCount++;

      if (this.mainUserList && this.mainUserList.length > 0) {
        clearInterval(checkInterval);
        this.getLeads(mainStatusArray);
      } else if (checkCount >= maxChecks) {
        clearInterval(checkInterval);
        this.getLeads(mainStatusArray);
      }
    }, 100);
  }

  getInventories(statusId, bool?) {
    if (bool) {
      this.currentPage = 1;
    }
    this.inventory = {};
    let obj = {
      mainStatusId: statusId,
      holdingSort: this.sort == 'DESC' ? true : false,
      page: this.currentPage,
      limit: this.currentLimit,
    };
    if (this.applySort) {
      obj = { ...obj, ...this.applySort };
    }
    this._loaderService.startLoader(statusId.length == 1 ? statusId[0] : 'ALL');
    this._inventoryService.getInventories(obj).subscribe(
      (response: ResponseModel) => {
        this._loaderService.stopLoader(
          statusId.length == 1 ? statusId[0] : 'ALL'
        );
        if (response.statusCode && response.statusCode == 200) {
          if (bool) {
            this.count += response?.data?.count || 0;
          }

          for (let i = 0; i < response?.data?.items?.length; i++) {
            let data = response.data.items[i];
            response.data.items[i].hordingPeriod =
              this.pipeline(data?.purchaseDate) + ' days';
          }
          this.inventory = response?.data;
        } else {
          this._toastrService.error(response.message, '');
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stopLoader(
          statusId.length == 1 ? statusId[0] : 'ALL'
        );
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }
  pipeline(first) {
    const nowDate = new Date().getTime();
    const createdDate = new Date(first).getTime();
    const differenceInTime = nowDate - createdDate;
    let days = Math.round(differenceInTime / (1000 * 3600 * 24));
    return days > 0 ? days : 0;
  }

  inventoryDetailsEmitter(item) {
    var statusIds = [item.mainStatusId];

    let obj = {
      mainStatusId: statusIds,
      holdingSort: this.sort == 'DESC' ? true : false,
    };

    if (this.applySort) {
      obj = { ...obj, ...this.applySort };
    }

    this._globalService.inventoryFilter = obj;

    this._globalService.isLeadPreviousEnable = true;
    this._globalService.isLeadNextEnable = true;
    this._globalService.currentModule = 'inventory';
    this._globalService.leadIdArray = [];
    localStorage.removeItem('RE-TAB');

    window.open(`/inventory/details?inventoryId=${item?._id}`);
    return;
  }

  getColumns() {
    this.loadingColumns = true;
    this._loaderService.start();
    const payload = { moduleId: this.moduleId, page: 1, limit: 10 };

    this._sharedService.getUserViewPreference(payload).subscribe(
      (response: ResponseModel) => {
        this._loaderService.stop();
        this.loadingColumns = false;

        if (response.statusCode === 200) {
          try {
            this.userViewPreferenceData = response?.data?.items?.[0];
            if (!this.userViewPreferenceData) {
              this.initializeStatusColumnConfigs();
              this.header = this.mapColumns(
                this.defaultColumnConfig,
                this.defaultColumns
              );
              this.updateColumns(
                this.defaultColumnConfig,
                !this.userViewPreferenceData?._id
              );
              return;
            }

            const config = JSON.parse(
              this.userViewPreferenceData.displayPreferences || '{}'
            );
            const { statusId } = this.getColumnMappings();
            if (config[statusId] && config[statusId]?.length) {
              this.initializeStatusColumnConfigs(config);
              this.header = this.mapColumns(
                config[statusId],
                this.defaultColumns
              );
            } else {
              this.initializeStatusColumnConfigs();
              this.header = this.mapColumns(
                this.defaultColumnConfig,
                this.defaultColumns
              );
              this.updateColumns(
                this.defaultColumnConfig,
                !this.userViewPreferenceData?._id
              );
              return;
            }
          } catch (error) {
            this.initializeStatusColumnConfigs();
            this.header = this.mapColumns(
              this.defaultColumnConfig,
              this.defaultColumns
            );
          }
        } else {
          this.initializeStatusColumnConfigs();
          this.header = this.mapColumns(
            this.defaultColumnConfig,
            this.defaultColumns
          );
        }
      },
      () => {
        this._loaderService.stop();
        this.loadingColumns = false;
        this.initializeStatusColumnConfigs();
        this.header = this.mapColumns(
          this.defaultColumnConfig,
          this.defaultColumns
        );
      }
    );
  }

  updateColumnsForStatus(statusId: string) {
    // Check if configuration exists for this status
    if (!this.statusColumnConfigs[statusId]) {
      // If not, create default configuration for this status
      const defaultColumns = this.getDefaultColumnsFromConstant();
      this.statusColumnConfigs[statusId] = this.enhanceColumns(defaultColumns);

      // Save this configuration if we have user preference data
      if (this.userViewPreferenceData) {
        const configUpdate = {};
        configUpdate[statusId] = defaultColumns;
        // Use save=false if we have an ID, otherwise use save=true
        this.updateColumns(configUpdate, !this.userViewPreferenceData._id);
      }
    }

    // Use the configuration for this status
    let configToUse = this.statusColumnConfigs[statusId];
    this.header = this.mapColumns(configToUse, this.defaultColumns);
    this.refreshLeadsGrid({ type: 'hot' });
  }

  loadUsersByVisibleRoles() {
    const teamAssignedCol = this.header.find(
      (col) => col.label === 'Team Assigned'
    );
    if (!teamAssignedCol || !teamAssignedCol.roles) return;

    const visibleRoles = teamAssignedCol.roles
      .filter((role) => role.isVisible)
      .map((role) => role.roleId);

    if (!visibleRoles.length) return;

    this._loaderService.start();
    this._userService.getUsersByRoles({ roles: visibleRoles }).subscribe(
      (response: ResponseModel) => {
        this._loaderService.stop();
        if (response.statusCode === 200) {
          this.mainUserList = response.data || [];
        }
      },
      () => this._loaderService.stop()
    );
  }

  // Get status configs for all statuses
  private getStatusConfigs(baseConfig?: any[]) {
    const source = baseConfig || this.statusColumnConfigs['default'];

    return {
      default: source,
      [StatusConstant.MainStatusId.LEAD]: source,
      [StatusConstant.LeadStatus.WARM_LEAD]: source,
      [StatusConstant.LeadStatus.DEAD_LEAD]: source,
      [StatusConstant.LeadStatus.REFERRED_TO_AGENT]: source,
    };
  }

  updateColumns(columns: any[] | { [key: string]: any[] }, save?: boolean) {
    const { statusId } = this.getColumnMappings();
    let config = {};

    if (this.userViewPreferenceData?.displayPreferences) {
      try {
        const existingConfig = JSON.parse(
          this.userViewPreferenceData.displayPreferences
        );
        if (existingConfig && typeof existingConfig === 'object') {
          config = JSON.parse(JSON.stringify(existingConfig));
        }
      } catch (e) {}
    }

    if (typeof columns === 'object' && !Array.isArray(columns)) {
      if (columns[statusId]) {
        const enhancedColumns = this.enhanceColumns(columns[statusId]);
        this.statusColumnConfigs[statusId] = enhancedColumns;
        config[statusId] = enhancedColumns;
      }
    } else if (Array.isArray(columns)) {
      const enhancedColumns = this.enhanceColumns(columns);
      this.statusColumnConfigs[statusId] = enhancedColumns;
      config[statusId] = enhancedColumns;
    }

    if (config['default']) delete config['default'];

    // Only make API call if explicitly requested with save=true
    if (Object.keys(config).length > 0) {
      const payload = {
        title: this.userViewPreferenceData?.title || 'userViewPreference',
        displayPreferences: JSON.stringify(config),
      };
      if (!save) {
        payload['userViewPreferenceId'] = this.userViewPreferenceData?._id;
      }
      if (save) {
        payload['moduleId'] = this.moduleId;
      }

      this._sharedService
        .updateUserViewPreference(payload, save)
        .subscribe((response) => {
          if (response.statusCode === 200) {
            this.userViewPreferenceData = response?.data;
          }
        });
    }
  }

  handleColumnConfig(columns: any, update?: boolean) {
    const { statusId } = this.getColumnMappings();
    const defaultColumns = this.getDefaultColumnsFromConstant();

    let configToUse: any[] = defaultColumns;
    if (columns && typeof columns === 'object' && !Array.isArray(columns)) {
      configToUse = columns[statusId] || defaultColumns;
    } else if (Array.isArray(columns)) {
      configToUse = columns;
    }

    configToUse = configToUse.map((col) => {
      if (col.label === 'Team Assigned') {
        col.hasRoles = true;

        // Initialize roles array if it doesn't exist
        if (!col.roles) {
          col.roles = Object.entries(MiscellaneousConstant.roleNames).map(
            ([roleId, roleName]) => ({
              roleId,
              roleName,
              isVisible: true,
            })
          );
        }
      }
      return col;
    });

    this.statusColumnConfigs[statusId] = this.enhanceColumns(configToUse);
    this.header = this.mapColumns(
      this.statusColumnConfigs[statusId],
      this.defaultColumns
    );

    // Only update if explicitly requested and not during initial load
    if (update && this.userViewPreferenceData) {
      this.updateColumns({ [statusId]: configToUse }, false);
    }
  }

  // Get default columns from leads-header-constant
  private getDefaultColumnsFromConstant() {
    return JSON.parse(JSON.stringify(LeadsHeaderConfig));
  }

  getColumnMappings(): { statusId: string } {
    // Handle different variations of lead type names
    let mappedType = this.leadType;

    if (this.leadType === 'warm' || this.leadType === 'warm lead') {
      mappedType = 'warm lead';
    } else if (this.leadType === 'dead' || this.leadType === 'dead lead') {
      mappedType = 'dead lead';
    } else if (
      this.leadType === 'referredtoagent' ||
      this.leadType === 'referred to agent'
    ) {
      mappedType = 'referred to agent';
    }

    const statusMap = {
      'warm lead': StatusConstant.LeadStatus.WARM_LEAD,
      'dead lead': StatusConstant.LeadStatus.DEAD_LEAD,
      'referred to agent': StatusConstant.LeadStatus.REFERRED_TO_AGENT,
      default: StatusConstant.MainStatusId.LEAD,
    };

    return { statusId: statusMap[mappedType] || statusMap['default'] };
  }

  // Ensure columns have necessary properties
  private enhanceColumns(columns: any[]): any[] {
    return columns.map((col) => {
      // Create a base column with standard properties
      const enhancedCol = {
        ...col,
        value:
          col.value || col.key || col.label?.toLowerCase().replace(/\s+/g, '_'),
        key: col.key || null,
      };

      // Handle Team Assigned column with roles
      if (col.label === 'Team Assigned') {
        // Ensure hasRoles property is set
        enhancedCol.hasRoles = true;

        // Initialize roles array if it doesn't exist
        if (!col.hasOwnProperty('roles') || !col.roles) {
          enhancedCol.roles = Object.entries(
            MiscellaneousConstant.roleNames
          ).map(([roleId, roleName]) => ({
            roleId,
            roleName,
            isVisible: true,
          }));
        }
        // Convert from old format if needed
        else if (col.roleVisibility && !col.roles[0]?.roleId) {
          enhancedCol.roles = Object.entries(
            MiscellaneousConstant.roleNames
          ).map(([roleId, roleName]) => ({
            roleId,
            roleName,
            isVisible: col.roleVisibility[roleId] === true,
          }));
        }
        // Copy existing roles array with proper structure
        else if (col.roles[0]?.roleId) {
          enhancedCol.roles = [...col.roles];
        }
      } else {
        // For other columns, just preserve properties if they exist
        if (col.hasOwnProperty('roles')) {
          enhancedCol.roles = [...col.roles];
        }

        if (col.hasRoles) {
          enhancedCol.hasRoles = col.hasRoles;
        }
      }

      return enhancedCol;
    });
  }

  // Map columns from config to definitions
  mapColumns(headerConfig: any[], columnDefs: any[]): any[] {
    // Create lookup map for column definitions
    const defMap = new Map(columnDefs.map((col) => [col.label, col]));

    // Find action column
    const actionCol = columnDefs.find((col) => col.key === 'action');

    // Get sorted visible columns from config (excluding action)
    const sortedColumns = [...headerConfig]
      .sort((a, b) => a.sort - b.sort)
      .map((h) => defMap.get(h.label))
      .filter((col) => col && col.key !== 'action');

    // Return columns with action at the end
    return actionCol ? [...sortedColumns, actionCol] : sortedColumns;
  }

  updateLeadStatusConfirm(event) {
    if (event && event.statusId && event.item) {
      const fromStatus = event.fromStatus;
      const toStatus = event.statusId;
      const item = event.item;
      const revertFn = event.revert; // Function to revert the status in the UI
      this.leadInfoList['leadData'] = { ...event.item };
      // If the status hasn't changed, do nothing
      if (fromStatus === toStatus) {
        return;
      }

      // Special case for wholesale leads with novation or wholesale in title
      if (
        item?.mainStatusId == StatusConstant.LeadStatus.UNDER_CONTRACT &&
        (item?.investmentTitle?.toLowerCase().includes('novation') ||
          item?.investmentTitle?.toLowerCase().includes('wholesale'))
      ) {
        this.dialogRef = this._dialog.open(ConfirmationDialogComponent, {
          width: '450px',
          data: {
            header: 'Confirmation',
            type: 'Leads',
            yesTitle: 'Yes, Continue',
            noTitle: 'Cancel',
            text: 'If you proceed with status change, your published listing will be removed. You will no longer see the listing tab in lead details.',
          },
        });
        this.dialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.isDeleteListing = true;
            if (this.leadInfoList['taskCount']) {
              this.updateLeadStatus(event);
            } else {
              this.getCount(item?._id, event);
            }
          }
        });
      } else {
        if (this.leadInfoList['taskCount']) {
          this.updateLeadStatus(event);
        } else {
          this.getCount(item?._id, event);
        }
      }
    }
  }
  updateLeadStatus(value) {
    //const fromStatus = this.leadInfoList?.leadData?.mainStatusTitle;
    const statusData = this.mainStatus;
    const type = statusData.filter((x) => x._id === value?.statusId)[0];
    //const toStatus = type.labels?.title || type?.name || type?.title;
    const fromStatus = value?.fromStatus;
    const toStatus = value.statusId;
    this.leadId = value?.item?._id;

    const taskStatusExclude = [
      StatusConstant.InventoryStatus.INVENTORY,
      StatusConstant.InventoryStatus.RENTAL,
      StatusConstant.InventoryStatus.SOLD,
    ];

    let checkResponse = this._globalService.statusChangeChecks({
      data: value?.item,
      fromStatus,
      toStatus,
      checkTask: true,
      checkPropertyAddress: true,
      allTasks: this.leadInfoList?.taskCount,
      module: 'Leads',
    });

    if (checkResponse?.success == 0) {
      if (checkResponse?.type == 2) {
        this.dialogRef = this._dialog.open(ConfirmationDialogComponent, {
          width: '450px',
          data: {
            header: 'Confirmation',
            type: 'Leads',
            yesTitle: 'Ok',
            hideNoButton: true,
            text: this.messageConstant.soldFromLeadsMessage,
          },
        });
      }
      if (checkResponse?.type == 3)
        this._toastrService.error(this.messageConstant.propertyAddressMissing);
      return;
    } else {
      let underContractDate;
      let offerCreatedDate;
      if (this.leadInfoList?.leadData?.underContractDate == undefined) {
        underContractDate = this.leadInfoList?.leadData.underContractDate;
      } else {
        underContractDate = new Date(
          Number(this.leadInfoList?.leadData?.underContractDate)
        );
      }

      if (this.leadInfoList?.leadData?.dateOfYourOffer == undefined) {
        offerCreatedDate = this.leadInfoList?.leadData?.dateOfYourOffer;
      } else {
        offerCreatedDate = new Date(
          Number(this.leadInfoList?.leadData?.dateOfYourOffer)
        );
      }
      let data = JSON.parse(JSON.stringify(this.leadInfoList?.leadData));

      if (checkResponse?.isTask) {
        // if (this.leadInfoList?.taskCount) {
        //   this._globalService['userData'] = {};
        //   this._globalService['userData']['pendingTaskDefault'] =
        //     'CHANGE_STATUS';
        // }
        let headerCaption =
          this.leadInfoList?.taskCount &&
          this.leadInfoList?.leadData?.dripId &&
          !taskStatusExclude.includes(toStatus)
            ? 'Pending Tasks & Drip Campaign'
            : this.leadInfoList?.leadData?.dripId &&
              !this.leadInfoList?.taskCount &&
              !taskStatusExclude.includes(toStatus)
            ? 'Drip Campaign'
            : 'Pending Tasks';
        const isChangeStatus =
          this._globalService?.userData?.pendingTaskDefault === 'CHANGE_STATUS';
        const hasDrip = !!this.leadInfoList?.leadData?.dripId;

        if (isChangeStatus && hasDrip && this.leadInfoList?.taskCount) {
          headerCaption = 'Pending Tasks & Drip Campaign';
        } else if (hasDrip) {
          headerCaption = 'Drip Campaign';
        } else if (isChangeStatus && this.leadInfoList?.taskCount) {
          headerCaption = 'Pending Tasks';
        }

        if (
          this._globalService?.userData?.pendingTaskDefault ===
            'CHANGE_STATUS' ||
          (this.leadInfoList?.leadData?.dripId &&
            toStatus !== StatusConstant.InventoryStatus.INVENTORY &&
            toStatus !== StatusConstant.InventoryStatus.SOLD)
        ) {
          this.dialogRef = this._dialog.open(TaskDripStatusChangeComponent, {
            width: '450px',
            data: {
              header: headerCaption,
              type: type,
              lead: this.leadInfoList?.leadData,
              taskCount: this.leadInfoList?.taskCount,
              pendingTaskDefault: this._globalService?.pendingTaskValue
                ? this._globalService?.pendingTaskValue
                : this._globalService?.userData?.pendingTaskDefault,
            },
          });
          this.dialogRef.afterClosed().subscribe((resultTaskDrip) => {
            if (resultTaskDrip) {
              this.callCheckForTaskAndPopup(
                this.leadInfoList?.leadData,
                type,
                toStatus,
                fromStatus,
                offerCreatedDate,
                underContractDate,
                checkResponse,
                resultTaskDrip
              );
            }
          });
        } else {
          this.callCheckForTaskAndPopup(
            this.leadInfoList?.leadData,
            type,
            toStatus,
            fromStatus,
            offerCreatedDate,
            underContractDate,
            checkResponse,
            this._globalService?.userData?.pendingTaskDefault
          );
        }
      } else {
        //change status without task
        this.callCheckForTaskAndPopup(
          this.leadInfoList?.leadData,
          type,
          toStatus,
          fromStatus,
          offerCreatedDate,
          underContractDate,
          checkResponse
        );
      }
    }
  }

  callCheckForTaskAndPopup(
    data,
    type,
    toStatus,
    fromStatus,
    offerCreatedDate,
    underContractDate,
    checkResponse,
    resultTaskDrip?
  ) {
    if (
      checkResponse?.showPopup &&
      checkResponse?.data &&
      !StatusConstant.LeadExcludeStatus.includes(toStatus)
    ) {
      //change status with popup and with task
      this.openModal(
        data,
        type,
        toStatus,
        fromStatus,
        new Date(Number(data?.leadCreated)),
        offerCreatedDate,
        underContractDate,
        checkResponse?.data,
        checkResponse?.fromKey,
        resultTaskDrip
      );
    } else {
      let result = {};
      result['isOverrideDrip'] = true;
      if (toStatus == StatusConstant.InventoryStatus.INVENTORY) {
        result['dripId'] = data?.dripId;
      } else {
        // result['dripId'] = resultTaskDrip?.dripStop ? data?.dripId : '';
        if (resultTaskDrip?.dripStop === 'isStopDrip') {
          result['dripId'] = data?.dripId;
          delete result['isOverrideDrip'];
        } else if (resultTaskDrip?.dripStop === 'isOverrideDrip') {
          result['dripId'] = data?.dripId;
          result['isOverrideDrip'] = true;
        } else if (resultTaskDrip?.dripStop === 'isKeepDrip') {
          delete result['dripId'];
          if (resultTaskDrip?.lead?.dripId == null) {
            result['isOverrideDrip'] = true;
          } else {
            delete result['isOverrideDrip'];
          }
        }
      }
      delete data['header'];
      result['taskTypeAssignment'] = resultTaskDrip?.taskTypeAssignment
        ? resultTaskDrip?.taskTypeAssignment
        : this._globalService?.userData?.pendingTaskDefault;

      if (resultTaskDrip?.isTaskCheck) {
        this.isTaskCheck = true;
        this._globalService.userData.pendingTaskDefault =
          result['taskTypeAssignment'];
      }

      this.changeStatus(
        { ...data, toStatus },
        { mainStatusId: type?._id, ...result }
      );
    }
  }

  changeStatus(lead, data?) {
    if (this.isTaskCheck) {
      this.pendingTaskDefaultSubmit(
        data?.taskTypeAssignment
          ? data?.taskTypeAssignment
          : lead?.taskTypeAssignment
      );
    }

    let apiCallOnCondition;
    let leadObj = {
      ...JSON.parse(JSON.stringify(lead)),
    };

    if (
      (this.type === 'wholesale' || lead?.calledForWholesale) &&
      !data?.calledForLead
    ) {
      delete leadObj['isSold'];
      delete leadObj['isInventory'];
      delete leadObj['calledForWholesale'];
      delete leadObj['toStatus'];
      delete leadObj['isRental'];
      delete leadObj['isDeadLead'];
      apiCallOnCondition = this._sharedService.changeWholesaleStatus(leadObj);
      console.log('leadObj', leadObj);
    } else {
      let obj: LeadsStatusChangeModel = {
        ...JSON.parse(JSON.stringify(data)),
      };
      if (!obj['dripId']) {
        delete obj['dripId'];
      }

      delete obj['inventory'];
      delete obj['assignedToBuyerToSold'];
      delete obj['offerType'];
      delete obj['calledForLead'];
      if (obj.mainStatusId != StatusConstant.LeadStatus.DEAD_LEAD) {
        obj['deadReasonIds'] = [];
        obj['deadReasonOtherText'] = '';
      }
      apiCallOnCondition = this._sharedService.changeLeadStatus(lead?._id, obj);
      console.log('obj', obj);
    }

    this._loaderService.start();
    apiCallOnCondition.subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          this._loaderService.stop();
          this.getLeads(this.mainStatusId, true);
          if (this.type != 'wholesale' || data?.calledForLead) {
            const status = this.mainStatus.filter(
              (x) => x._id === response?.data?.mainStatusId
            )[0];
            let isInventory = data?.inventory
              ? Object.keys(data?.inventory)?.length
              : '';
            const leadData = {
              leadId: lead?._id,
              dateOfYourOffer: data?.dateOfYourOffer,
              offerBy: 'our_offer',
              yourOfferPrice: data?.yourOfferPrice,
              offerType: data?.offerType,
            };

            this.mainStatusId = status?._id;
            this.leadsInfoComponent?.getContingencies(true);
            if (data.existingTypeId) {
              const type = this.investmentType?.find(
                (x) => x?._id === data?.existingTypeId
              );
              if (type) lead.investmentTitle = type?.name;
            }
            // this._commonFunction.confirmCondition(
            //   lead?.investmentTitle,
            //   data?.isUnderContract
            // );
            if (this.isDeleteListing) {
              this.listingComponent?.deleteListing(this.leadId);
              this.isDeleteListing = false;
            }
            if (isInventory) {
              this.addInventory(lead?._id, leadObj, data?.inventory, data);
            } else {
              this._loaderService.stop();
              let component = LeadsStatusChangeComponent;
              if (data.isOffersMade) {
                this.dialogRef = this._dialog.open(component, {
                  width: '300px',
                  data: {
                    offerMadeUpdate: true,
                  },
                });
                setTimeout(() => {
                  this.dialogRef.close();
                }, 4000);
              }
              if (data.isUnderContract) {
                this.dialogRef = this._dialog.open(component, {
                  maxWidth: '100vw',
                  maxHeight: '100vh',
                  height: '100%',
                  width: '100%',
                  panelClass: 're-fullscreen-modal',
                  data: {
                    isUnderContract: true,
                  },
                });
                setTimeout(() => {
                  this.dialogRef.close();
                }, 4000);
              }
              this.getMainStatus();
              this.getCount(lead?._id);
              // this.getCount(lead?._id);
            }
            if (status?.title == 'dead lead') {
              this._router.navigate([`/leads/details`], {
                queryParams: { leadsId: response.data._id },
              });
            }
            this._sharedService.refreshActivityLog.next(true);
            this._sharedService.refreshListTask.next(true);
          } else {
            if (data?.mainStatusId) {
              this.changeStatus(
                { ...this.leadInfoList?.leadData, toStatus: lead?.toStatus },
                {
                  ...data,
                  calledForLead: true,
                }
              );
            } else {
              this.leadsInfoComponent?.getContingencies(true);
              this._sharedService?.refreshActivityLog?.next(true);
              this._sharedService?.refreshListTask?.next(true);
            }
          }

          if (
            this.leadInfoList?.leadData?.projectTypeId &&
            lead?.toStatus &&
            StatusConstant.LeadStatus.UNDER_CONTRACT == lead?.toStatus &&
            this.leadInfoList?.leadData?.investmentTitle &&
            (this.leadInfoList?.leadData?.investmentTitle
              ?.toLowerCase()
              .includes('novation') ||
              this.leadInfoList?.leadData?.investmentTitle
                ?.toLowerCase()
                .includes('wholesale'))
          ) {
            this.changeInvestmentStatus(this.leadInfoList?.leadData, {
              projectTypeId: data?.existingTypeId
                ? data?.existingTypeId
                : this.leadInfoList?.leadData?.projectTypeId,
            });
          }
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }
  changeInvestmentStatus(lead, data, redirect?) {
    let obj: LeadsStatusChangeModel = {
      ...JSON.parse(JSON.stringify(data)),
    };
    this._loaderService.start();
    this._sharedService.changeLeadInvestmentStatus(lead?._id, obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          this._loaderService.stop();
          if (redirect) {
            this._router.navigate(['/transaction-management']);
          } else {
            this._sharedService.refreshActivityLog.next(true);
            this._sharedService.refreshListTask.next(true);
          }
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }
  pendingTaskDefaultSubmit(type) {
    this._loaderService.start();

    let obj = {
      pendingTaskDefault: type,
    };

    this._userService.pendingTaskDefaults(obj).subscribe(
      (response: ResponseModel) => {
        let pendingTaskData = response.data || [];
        this._globalService.pendingTaskValue = pendingTaskData;
        this._loaderService.stop();
      },
      (err) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  openModal(
    data,
    type,
    toStatus,
    fromStatus,
    leadCreatedDate,
    offerCreatedDate,
    underContractDate,
    isVisibleData,
    fromKey,
    resultTaskDrip?
  ) {
    let component;

    if (this.type !== 'wholesale') {
      component = LeadsStatusChangeComponent;
    } else if (this.type === 'wholesale' && fromStatus) {
      if (
        toStatus == StatusConstant.InventoryStatus.INVENTORY ||
        toStatus == StatusConstant.InventoryStatus.NEW_INVENTORY ||
        toStatus == StatusConstant.LeadStatus.DEAD_LEAD
      ) {
        component = LeadsStatusChangeComponent;
      } else {
        component = WholesalePipelineStatusChangeComponent;
      }
    }
    let leadData1, leadData2;
    this.dialogRef = this._dialog.open(component, {
      width: '600px',
      data: {
        header: type?.labels?.title ? type?.labels?.title : type?.title,
        status: { to: toStatus, from: fromStatus, toId: type?._id },
        moduleId: this.moduleId,
        subModuleId: data?._id,
        page: this.currentPage,
        limit: this.currentLimit,
        leadCreatedDate,
        offerCreatedDate,
        underContractDate,
        item: data,
        mainStatus: this.mainStatus,
        isVisibleData: isVisibleData,
        fromKey: fromKey,
      },
    });

    this.dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (resultTaskDrip) {
          result['isOverrideDrip'] = true;
          if (toStatus == StatusConstant.LeadStatus.INVENTORY) {
            result['dripId'] = data?.dripId;
          } else {
            // result['dripId'] = resultTaskDrip?.dripStop ? data?.dripId : '';
            if (resultTaskDrip?.dripStop === 'isStopDrip') {
              result['dripId'] = data?.dripId;
              delete result['isOverrideDrip'];
            } else if (resultTaskDrip?.dripStop === 'isOverrideDrip') {
              result['dripId'] = data?.dripId;
              result['isOverrideDrip'] = true;
            } else if (resultTaskDrip?.dripStop === 'isKeepDrip') {
              delete result['dripId'];
              if (resultTaskDrip?.lead?.dripId == null) {
                result['isOverrideDrip'] = true;
              } else {
                delete result['isOverrideDrip'];
              }
            }
          }
          delete data['header'];
          result['taskTypeAssignment'] = resultTaskDrip.taskTypeAssignment;

          if (resultTaskDrip?.isTaskCheck) {
            this.isTaskCheck = true;
            this._globalService.userData.pendingTaskDefault =
              result.taskTypeAssignment;
          }
        } else {
          delete data['header'];
          data['taskTypeAssignment'] = 'NONE';
          // Drip
          result['isOverrideDrip'] = true;
          delete result['dripId'];
          if (
            toStatus == StatusConstant.LeadStatus.INVENTORY ||
            toStatus == StatusConstant.InventoryStatus.SOLD
          ) {
            result['dripId'] = data?.dripId;
          }
        }
        leadData1 = data;
        leadData2 = { mainStatusId: type?._id, ...result };

        if (resultTaskDrip?.isTaskCheck) {
          this.isTaskCheck = true;
        }

        this.changeStatus({ ...leadData1, toStatus }, leadData2);
      }
    });
  }

  addInventory(leadId, lead, inventory, data?) {
    const obj = {
      address: lead?.address,
      //unitNo: lead?.unitNo,
      addedFrom: 'CRM',
      leadId,
      latitude: lead?.latitude,
      longitude: lead?.longitude,
      isFromLead: true,
      // dripId: data.dripId,
      taskTypeAssignment: data.taskTypeAssignment || 'NONE',
      ...inventory,
    };
    obj['purchasePrice'] = obj['purchasePrice'] ? obj['purchasePrice'] : 0;
    obj['salesPrice'] = obj['salesPrice'] ? obj['salesPrice'] : 0;
    this._inventoryService.addInventory(obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode === 200) {
          this._loaderService.stop();

          if (
            response.data.mainStatusId == StatusConstant.InventoryStatus.SOLD
          ) {
            this._router.navigate([`/sold/details`], {
              queryParams: { soldId: response.data._id },
            });
          } else if (
            response.data.mainStatusId == StatusConstant.InventoryStatus.RENTAL
          ) {
            this._router.navigate([`/rental/details`], {
              queryParams: { rentalId: response.data._id },
            });
          } else {
            this._router.navigate([`/inventory/details`], {
              queryParams: { inventoryId: response.data._id },
            });
          }
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  getCount(leadId, update?) {
    const obj = { leadId: leadId };
    this._loaderService.start();
    this._leadService.getCount(obj).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          this.leadCountData = response?.data;
          this.leadInfoList['activityCount'] =
            this.leadCountData?.activityCount || 0;
          this.leadInfoList['appointmentCount'] =
            this.leadCountData?.appointmentCount || 0;
          this.leadInfoList['esignCount'] = this.leadCountData?.esignCount || 0;
          this.leadInfoList['fileCount'] = this.leadCountData?.fileCount || 0;
          this.leadInfoList['linkCount'] = this.leadCountData?.linkCount || 0;
          this.leadInfoList['relatedLeadCount'] =
            this.leadCountData?.relatedLeadCount || 0;
          this.leadInfoList['taskCount'] = this.leadCountData?.taskCount || 0;
          this.taskAppointmentCount =
            this.leadCountData?.appointmentCount +
            this.leadCountData?.taskCount;
          this._loaderService.stop();
          if (update) {
            this.updateLeadStatus(update);
          }
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          // const error: ResponseModel = err.error;
          // this._toastrService.error(error.message, '');
          this._loaderService.stop();
          this._router.navigate(['/leads']);
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }
}
