//MODULE
import {
  Component,
  OnInit,
  Inject,
  HostListener,
  ViewChild,
  ElementRef,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import * as moment from 'moment';
import { startWith, map } from 'rxjs/operators';

//UTILS
import { MessageConstant } from 'src/app/utils/message-constant';
import { QuickFilterParameter } from 'src/app/utils/quick-filter';
import { ListBuildingQuickFilterParameter } from 'src/app/utils/list-building-quick-filter';
import { StatusConstant } from 'src/app/utils/status-constant';
import { ErrorModel } from 'src/app/utils/models/error';
import { ResponseModel } from 'src/app/utils/models/response';
import { FilterListModel } from 'src/app/utils/models/filter';
import { ListStackingCodeJson } from 'src/app/utils/list-stacking-code-json';
import { MiscellaneousConstant } from 'src/app/utils/miscellaneous-constant';
import { RoleObject } from 'src/app/utils/roleObject';
import { countyConstant } from 'src/app/utils/county-constant';

//SERVICES
import { ToastrService } from 'ngx-toastr';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { SharedService } from 'src/app/shared/shared.service';
import { MasterFilterService } from 'src/app/providers/masterFilter/master-filter.service';
import { UserService } from 'src/app/providers/user/user.service';
import { CommonFunctionsService } from 'src/app/utils/common-functions/common-functions.service';
import { MarketsService } from 'src/app/providers/markets/markets.service';
import { TaggingService } from 'src/app/providers/tagging/tagging.service';
import { InventoryService } from 'src/app/providers/inventory/inventory.service';
import { LeadsService } from 'src/app/providers/leads/leads.service';
import { DripService } from 'src/app/providers/drip/drip.service';
import { TaskService } from 'src/app/providers/task/task.service';
import { PreferenceService } from 'src/app/providers/preference/preference.service';
import { BuyerFilterParameter } from 'src/app/utils/buyers-filter';
import { BuyersService } from 'src/app/providers/buyers/buyers.service';
import { Observable } from 'rxjs';
import { AccountingService } from 'src/app/providers/accounting/accounting.service';
import { VendorsService } from 'src/app/providers/vendors/vendors.service';
import { VendorFilterParameter } from 'src/app/utils/vendors-filter';
import { ListBuildingService } from 'src/app/providers/list-building/list-building.service';
import { CashBuyerPrameter } from 'src/app/utils/cash-buyer-filter';
import { kpiGoalFilter } from 'src/app/utils/kpi-goal-filter';
import { kpiByTagFilterParameter } from 'src/app/utils/kpi-by-tag-filter';
import { CeoDashboardPrameter } from 'src/app/utils/ceo-dashboard-filter';
import { cmparisonDashboardFilter } from 'src/app/utils/comparison-dashboard-filter';
import { individualDashboardFilterParameter } from 'src/app/utils/individual-dashboard-filter';
import { campaignDashboardFilter } from 'src/app/utils/campaign-dashboard-filter';
import { leadSourceDashboardFilter } from 'src/app/utils/lead-source-dashboard-filter';

@Component({
  selector: 'app-quick-filter-dialog',
  templateUrl: './quick-filter-dialog.component.html',
  styleUrls: ['./quick-filter-dialog.component.scss'],
})
export class QuickFilterDialogComponent implements OnInit {
  @ViewChild('search') searchElement: ElementRef;

  @HostListener('window:click', ['$event.target'])
  checkActionModal($event) {
    const selectField = document.getElementById('re-select-field');
    if (selectField) {
      const isClickInside = selectField.contains(<HTMLInputElement>$event);

      if (!isClickInside) {
        this.isMenu = false;
      }
    }
  }

  listStackingCodeJson = ListStackingCodeJson;

  tabIndex: number = 1;
  // filterParameters: FilterListModel[] = [];
  // filterParametersMain: FilterListModel[] = [];
  filterParameters: any = [];
  filterParametersMain: any = [];
  selectedFilter = [];
  selectedFilterMain = [];
  event: any;
  step1: boolean = true;
  step2: boolean = false;
  step3: boolean = false;
  isSubmitted: boolean = false;
  isSaveFilter: boolean = false;
  isShare: boolean = false;
  filterDetailForm: FormGroup;
  messageConstant = MessageConstant;
  filterGroup: any = [];
  filterShowGroup: any = [];
  selectedFilterGroup: any = [];
  selectedFilterShowGroup: any = [];

  isEdit: boolean = false;
  viewMode: boolean = false;

  fieldFind: any;
  selectFieldFind: any;

  //user permission
  userFind: any;
  userList: any = [];
  userListMain: any = [];
  userPermission: any = [];
  userPermissionMain: any = [];
  isUserView: boolean = false;

  //save location
  folderList: any = [];
  folderListMain: any = [];
  isMenu: boolean = false;
  selectedFolderData: any = {};
  fieldFolder: any;
  individualObj: any = {
    _id: 'individual',
    title: 'Save as an Individual Filter',
  };

  //multi-selected options array
  marketList: any = [];
  roleList: any = [];
  mainUserList: any = [];
  allMemberList: any = [];
  tagList: any = [];
  projectTypeList: any = [];
  campaignList: any = [];
  crmQuestionList: any = [];
  mainStatusList: any = [];
  proprtyTypeList: any = [];
  numberTypeList: any = [
    { label: 'Voip', value: 'voip' },
    { label: 'Landline', value: 'landline' },
    { label: 'Mobile', value: 'mobile' },
    { label: 'Toll-free', value: 'toll-free' },
    { label: 'Invalid', value: 'invalid' },
  ];
  dripList: any = [];
  taskList: any = [];
  cityList: any = [];
  stateList: any = [];
  zipList: any = [];
  userRoleList: any = [];
  deadReasons: any[] = [];
  targetGeographicInfoLimit: number = 100;

  //dates
  conditionalDate: any = [
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'week' },
    { label: 'This Week to Date', value: 'weekToDate' },
    { label: 'This Month', value: 'month' },
    { label: 'This Month to Date', value: 'monthToDate' },
    { label: 'This Quarter', value: 'quarter' },
    { label: 'This Quarter to Date', value: 'quarterToDate' },
    { label: 'This Year', value: 'year' },
    { label: 'This Year to Date', value: 'yearToDate' },
    { label: 'Yesterday', value: 'yesterday' },
    { label: 'Last Week', value: 'lastWeek' },
    { label: 'Last week to Date', value: 'lastWeekToDate' },
    { label: 'Last Month', value: 'lastMonth' },
    { label: 'Last Month to Date', value: 'lastMonthToDate' },
    { label: 'Last Quarter', value: 'lastQuarter' },
    { label: 'Last Quarter to Date', value: 'lastQuarterToDate' },
    { label: 'Last Year', value: 'lastYear' },
    { label: 'Last Year to Date', value: 'lastYearToDate' },
    { label: 'Last 7 Days', value: 'last7Days' },
    { label: 'Last 14 Days', value: 'last14Days' },
    { label: 'Last 30 Days', value: 'last30Days' },
    { label: 'Last 60 Days', value: 'last60Days' },
    { label: 'Last 90 Days', value: 'last90Days' },
    // { label: 'Next Week', value: 'nextWeek' },
    // { label: 'Next 2 weeks', value: 'next2Weeks' },
    // { label: 'Next 4 weeks', value: 'next4Weeks' },
    // { label: 'Next Month', value: 'nextMonth' },
    // { label: 'Next Quarter', value: 'nextQuarter' },
    // { label: 'Next Year', value: 'nextYear' },
  ];

  conditionalTime: any = [
    { label: 'Minutes ', value: 'minutes' },
    { label: 'Hours', value: 'hours' },
    { label: 'Days', value: 'days' },
    { label: 'Week', value: 'week' },
    { label: 'Month', value: 'month' },
    { label: 'Year', value: 'year' },
  ];
  editFilterData: any = {};
  userData: any = {};
  tagType: any;

  allTagList: any[] = [];
  tagControl = new FormControl();
  filteredTags: Observable<any[]>;

  unavailableColorIndex: any[] = [];
  unavailableColorIndexResponse: any[] = [];
  colorCodes = MiscellaneousConstant.colorCodes;

  suggestionList: any[] = [];
  appliedFilteredList: FilterListModel[] = [];

  cityControl = new FormControl();
  filteredCities: Observable<any[]>;
  allCitiesList: any[] = [];

  zipControl = new FormControl();
  filteredZip: Observable<any[]>;
  allZipList: any[] = [];

  countyControl = new FormControl();
  filteredCounties: Observable<any[]>;
  allCountiesList: any[] = [];

  stateControl = new FormControl();
  filteredStates: Observable<any[]>;
  allStatesList: any[] = [];

  isColumnsToDisplay: boolean = false;
  isDataToDisplay: boolean = false;
  isIndividualDashboard: boolean = false;
  isComparisonDashboard: boolean = false;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialog: MatDialogRef<QuickFilterDialogComponent>,
    private formBuilder: FormBuilder,
    private _masterFilterService: MasterFilterService,
    private _loaderService: NgxUiLoaderService,
    private _toastrService: ToastrService,
    public _sharedService: SharedService,
    public _userService: UserService,
    public _commonFunctionService: CommonFunctionsService,
    private _marketServices: MarketsService,
    private _taggingService: TaggingService,
    private _inventoryService: InventoryService,
    private _leadsService: LeadsService,
    private _dripService: DripService,
    private _taskService: TaskService,
    private _preferenceService: PreferenceService,
    private _buyersService: BuyersService,
    private _accountingService: AccountingService,
    private _vendorsService: VendorsService,
    private _listBuildingService: ListBuildingService
  ) {
    this.filterDetailForm = this.formBuilder.group({
      filterTitle: [
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern('^\\S.*\\S$|^\\S$'),
        ]),
      ],
      filterDescription: [''],
    });
  }

  async ngOnInit() {
    await this.getUserDetails();
    // let filterDataView = QuickFilterParameter.Filters.filter((x) =>
    //   this.data.view == 'GRID' ? x?.viewOn == 'all' : x?.value
    // );

    // let filterDataView = QuickFilterParameter.Filters.filter((x) =>
    //   this.data.view == 'GRID' ? x?.viewOn == 'all' : x?.value
    // ).map((x, index) => {
    //   return {
    //     ...x,
    //     position: index,
    //   };
    // });
    let moduleWiseFilter: any = QuickFilterParameter?.Filters || [];
    if (this.data.moduleId == '67567bb418d5ea6344a6afee') {
      moduleWiseFilter = ListBuildingQuickFilterParameter?.Filters || [];
    }
    let filterDataView: any = moduleWiseFilter.map((x, index) => {
      return {
        ...x,
        position: index,
      };
    });
    const leadModuleId = StatusConstant.MainStatusId.LEAD;
    const buyersModuleId = StatusConstant.MainStatusId.BUYER;
    const vendorModuleId = StatusConstant.MainStatusId.VENDOR;
    const wholsalePiplineModuleId =
      StatusConstant.MainStatusId.WHOLESALEPIPELINE;
    const cashBuyerModuleId = StatusConstant.MainStatusId.CASHBUYER;
    const kpiGoalModuleId = StatusConstant.MainStatusId.KPIGOAL;
    const kpiByTagModuleId = StatusConstant.MainStatusId.KPIBYTAG;
    const ceoDashboartdModuleId = StatusConstant.MainStatusId.CEODASHBOARD;
    const comparisonDashboardId =
      StatusConstant.MainStatusId.COMPARISON_DASHBOARD;
    const comparisonLeadSourceDashboardId =
      StatusConstant.MainStatusId.COMPARISON_LEAD_SOURCE_DASHBOARD;
    const comparisonCampaignNameDashboardId =
      StatusConstant.MainStatusId.COMPARISON_CAMPAIGN_NAME_DASHBOARD;
    const individualDashboardModuleId =
      StatusConstant.MainStatusId.INDIVIDUALDASHBOARD;

    this.isIndividualDashboard =
      this.data.moduleId === individualDashboardModuleId;

    // Comparison Dashboard
    this.isComparisonDashboard = [
      comparisonDashboardId,
      comparisonLeadSourceDashboardId,
      comparisonCampaignNameDashboardId,
    ].includes(this.data.moduleId);

    switch (this.data.moduleId) {
      case leadModuleId:
        this.tagType = '1';
        break;
      case wholsalePiplineModuleId:
        this.tagType = '1';
        break;
      case buyersModuleId:
        this.tagType = '2';
        break;
      case vendorModuleId:
        this.tagType = '3';
        break;
      case kpiByTagModuleId:
        this.tagType = '1';
        break;
    }

    switch (this.data.moduleId) {
      case leadModuleId:
        filterDataView = QuickFilterParameter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case wholsalePiplineModuleId:
        filterDataView = QuickFilterParameter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;

      case buyersModuleId:
        filterDataView = BuyerFilterParameter.Filters.map((x, index) => {
          if (
            this.data?.fromAudience &&
            x.value === 'email' &&
            !this.data?.isEdit
          ) {
            x.checked = true;
            x.selectedOperator = 'is';
            x.selectedOption = 'yes';
            setTimeout(() => {
              this.checkedValue({ target: { checked: true } }, index);
            }, 1000);
          }
          return {
            ...x,
            position: index,
          };
        });
        break;
      case vendorModuleId:
        filterDataView = VendorFilterParameter.Filters.map((x, index) => {
          if (
            this.data?.fromAudience &&
            x.value === 'email' &&
            !this.data?.isEdit
          ) {
            x.checked = true;
            x.selectedOperator = 'is';
            x.selectedOption = ['yes'];
            setTimeout(() => {
              this.checkedValue({ target: { checked: true } }, index);
            }, 1000);
          }
          return {
            ...x,
            position: index,
          };
        });
        break;

      case cashBuyerModuleId:
        filterDataView = CashBuyerPrameter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case kpiByTagModuleId:
        filterDataView = kpiByTagFilterParameter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case individualDashboardModuleId:
        filterDataView = individualDashboardFilterParameter.Filters.map(
          (x, index) => {
            return {
              ...x,
              position: index,
            };
          }
        );
        break;
      case kpiGoalModuleId:
        filterDataView = kpiGoalFilter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case ceoDashboartdModuleId:
        filterDataView = CeoDashboardPrameter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case comparisonDashboardId:
        filterDataView = cmparisonDashboardFilter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case comparisonLeadSourceDashboardId:
        filterDataView = leadSourceDashboardFilter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
      case comparisonCampaignNameDashboardId:
        filterDataView = campaignDashboardFilter.Filters.map((x, index) => {
          return {
            ...x,
            position: index,
          };
        });
        break;
    }

    // let filterDataView = QuickFilterParameter.Filters.map((x, index) => {
    //   return {
    //     ...x,
    //     position: index,
    //   };
    // });

    this.filterParameters = JSON.parse(JSON.stringify(filterDataView));
    this.filterParametersMain = JSON.parse(JSON.stringify(filterDataView));

    if (this.data?.isEdit) {
      this.isEdit = true;
    }

    if (this.data?.viewMode) {
      this.viewMode = true;
    }

    this.folderList = this.data.folderList;
    this.folderListMain = this.data.folderList;

    if (this.data?.isFolder) {
      this.selectedFolderData = this.data?.folderData;
    } else {
      this.selectedFolderData = this.individualObj;
    }

    if (this.isIndividualDashboard || this.isComparisonDashboard) {
      const columnOnly = this.filterParameters.filter((f) => f.isColumns);
      this.filterParameters = this.filterParameters.filter((f) => !f.isColumns);

      // set selectedFilter & selectedFilterMain to _only_ the columns
      this.selectedFilter = [...columnOnly];
      this.selectedFilterMain = [...columnOnly];

      await this.groupingFilter(this.filterParameters);
      this.settleFilter();
    } else {
      await this.groupingFilter(this.filterParameters);
    }

    // await this.getSubUserList();
    // await this.getUser();
    // await this.getTaskList();

    // if (this.userData?._id != undefined && !this.isEdit) {
    //   this.userPermissionMain = [
    //     {
    //       _id: this.userData?._id,
    //       name: this.userData?.firstName + ' ' + this.userData?.lastName,
    //       permission: 1,
    //       profileImage: this.userData?.profileImage,
    //     },
    //   ];

    //   this.userPermission = this.userPermissionMain.map((item) => ({
    //     userId: item?._id,
    //     permission: item?.permission,
    //   }));

    //   this.isUserView = this.userPermission.some(
    //     (record) => record.permission === 1
    //   );
    // }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.searchElement?.nativeElement?.focus();
    }, 500);
  }

  groupingFilter(fArray) {
    let index;
    let index1;
    for (let i = 0; i < fArray.length; i++) {
      fArray[i]['show'] = 1;
      index = this.filterGroup.findIndex(
        (item) => item.name === fArray[i]?.filterGroup
      );
      index1 = this.filterShowGroup.findIndex(
        (item) => item.name === fArray[i]?.filterGroup
      );
      if (fArray[i]?.filterGroup && !fArray[i]?.isColumns && index < 0)
        this.filterGroup.push({
          name: fArray[i]?.filterGroup,
          show: 1,
        });
      if (fArray[i]?.filterGroup && fArray[i]?.isColumns && index1 < 0)
        this.filterShowGroup.push({
          name: fArray[i]?.filterGroup,
          show: 1,
          isColumns: fArray[i]?.isColumns,
        });
      this.isDataToDisplay = fArray[i]?.isColumns ? true : false;
    }
  }

  get filterTitle() {
    return this.filterDetailForm.get('filterTitle');
  }

  tabError() {
    return this.selectedFilterMain.some((x) => {
      if (
        x.optionsType === 'SELECT' ||
        x.optionsType === 'MULTI-SELECT' ||
        x.optionsType === 'MULTI-SELECT-TITLE'
      ) {
        return !x.selectedOption || x.selectedOption.length === 0;
      }

      if (x.optionsType === 'RANGE') {
        if (x.selectedOperator !== 'between') {
          return !x.minVal;
        } else {
          return !x.minVal || !x.maxVal;
        }
      }

      if (x.optionsType === 'DATE-RANGE') {
        if (x.selectedOperator == 'is') {
          return !x.selectedCondition;
        } else {
          if (x.selectedOperator !== 'between') {
            return !x.minVal;
          } else {
            return !x.minVal || !x.maxVal;
          }
        }
      }

      if (
        x.optionsType === 'TIME-RANGE' ||
        x.optionsType === 'TIME-RANGE-ZERO'
      ) {
        return !x.selectedOption || !x.selectedCondition;
      }

      if (x.optionsType === 'DATE-SCORE') {
        return !x.selectedOption || !x.selectedCondition;
      }

      if (!x?.selectedOperator) {
        return true;
      } else {
        return false;
      }
    });
  }

  async tabChange(index, bool?) {
    let hasTabError = await this.tabError();
    if (
      this.tabIndex == 1 &&
      this.selectedFilter.length > 0 &&
      index == 3 &&
      !this.viewMode &&
      hasTabError
    ) {
      return;
    }

    if (bool) {
      let hasListBuyingCompulsoryField = false;
      this.selectedFilterMain.map((x) => {
        //for select
        if (x.optionsType === 'SELECT' && this.tabIndex === 2) {
          x.error = !x.selectedOption || x.selectedOption.length === 0;
        }

        //for operator
        if (!x?.selectedOperator && this.tabIndex === 2) {
          x.operatorError = true;
        } else {
          x.operatorError = false;
        }

        //for date range
        if (x.optionsType === 'DATE-RANGE' && this.tabIndex === 2) {
          if (x?.selectedOperator != 'is') {
            // For start date
            if (!x.minVal) {
              x.error = x.errorStartDate = true;
              x.rangeErrorMsg = 'Please add Date';
            } else {
              x.errorStartDate = false;
              if (x.selectedOperator !== 'between') {
                x.error = false;
                x.rangeErrorMsg = '';
              }
            }

            // For end date
            if (!x.maxVal && x.selectedOperator === 'between') {
              x.error = x.errorEndDate = true;
              x.rangeErrorMsg = 'Please add Date';
            } else {
              x.errorEndDate = false;
              if (x.minVal) {
                x.error = false;
                x.rangeErrorMsg = '';
              }
            }
          }
        }

        //for range
        if (x.optionsType === 'RANGE' && this.tabIndex === 2) {
          // For min value
          if (
            x.minVal == null ||
            x.minVal == undefined ||
            x.minVal == '' ||
            !x.noMinValidatation
          ) {
            x.error = x.errorMinVal = true;
            x.rangeErrorMsg = 'Please add value';
          } else {
            x.errorMinVal = false;
            if (x.selectedOperator !== 'between') {
              x.error = false;
              x.rangeErrorMsg = '';
            }
          }

          // For max value
          if (!x.maxVal && x.selectedOperator === 'between') {
            x.error = x.errorMaxVal = true;
            x.rangeErrorMsg = 'Please add value';
          } else {
            x.errorMaxVal = false;
            if (x.minVal) {
              x.error = false;
              x.rangeErrorMsg = '';
            }
          }

          // For value error
          if (x.minVal && x.maxVal && x.minVal >= x.maxVal) {
            x.error = x.errorMaxVal = true;
            x.rangeErrorMsg = 'Max. value less than or equal to Min. value';
          }
        }

        // For time range and multi-select
        if (
          (x.optionsType === 'TIME-RANGE' ||
            x.optionsType === 'TIME-RANGE-ZERO' ||
            x.optionsType === 'MULTI-SELECT' ||
            x.optionsType === 'MULTI-SELECT-TITLE') &&
          this.tabIndex === 2
        ) {
          //For email stats & sms stats Enable
          try {
            if (x.value === 'emailCampaign' && x?.selectedOption?.length > 0) {
              this.filterParametersMain.map((a) => {
                if (a.value === 'emailStats') {
                  a.disabled = false;
                } else if (
                  !x.selectedOption ||
                  x.selectedOption == '' ||
                  x.selectedOption.length == 0
                ) {
                  a.disabled = true;
                }
                return a;
              });
              this.filterParameters.map((a) => {
                if (a.value === 'emailStats') {
                  a.disabled = false;
                } else if (
                  !x.selectedOption ||
                  x.selectedOption == '' ||
                  x.selectedOption.length == 0
                ) {
                  a.disabled = true;
                }
                return a;
              });
            }
            if (x.value === 'smsCampaign' && x?.selectedOption?.length > 0) {
              this.filterParametersMain.map((a) => {
                if (a.value === 'smsStats') {
                  a.disabled = false;
                }
                return a;
              });
              this.filterParameters.map((a) => {
                if (a.value === 'smsStats') {
                  a.disabled = false;
                }
                return a;
              });
            }
          } catch (error) {
            console.error(error);
          }
          if (
            !x.selectedOption ||
            x.selectedOption == '' ||
            x.selectedOption.length == 0
          ) {
            x.error = x.errorMinVal = true;
            x.rangeErrorMsg = 'Please add value';
          } else {
            x.error = x.errorMinVal = false;
            x.rangeErrorMsg = '';
          }

          if (
            x.optionsType === 'TIME-RANGE' ||
            x.optionsType === 'TIME-RANGE-ZERO'
          ) {
            x.error = x.conditionError = false;
            x.rangeErrorMsg = '';
            if (!x.selectedCondition) {
              x.error = x.conditionError = true;
              x.rangeErrorMsg = 'Please add value';
            }
            if (!x.selectedOption) {
              x.error = x.errorMinVal = true;
              x.rangeErrorMsg = 'Please add value';
            }
          }
        }

        if (x.optionsType === 'DATE-SCORE') {
          x.error = x.conditionError = false;
          x.rangeErrorMsg = '';
          if (!x.selectedCondition) {
            x.error = x.conditionError = true;
            x.rangeErrorMsg = 'Please add value';
          }
          if (!x.selectedOption) {
            x.error = x.errorMinVal = true;
            x.rangeErrorMsg = 'Please add value';
          }
          if (!x.minVal) {
            x.error = x.errorStartDate = true;
            x.rangeErrorMsg = 'Please add Date';
          } else {
            x.errorStartDate = false;
            x.error = false;
            x.rangeErrorMsg = '';
          }
        }

        //for custom questions
        if (
          (x.value == 'leadCustomQuestions' && index === 3) ||
          (x.value == 'buyerCustomQuestions' &&
            this.tabIndex === 2 &&
            index === 3)
        ) {
          const hasFinalError = x.masterOptions.filter(
            (item) => !item.selectedOption && !item.minVal && !item.maxVal
          );

          if (hasFinalError.length == x.masterOptions.length) {
            x.error = true;
            this._toastrService.error(
              this.messageConstant.quickFilterSelectField
            );
          } else {
            x.error = false;
          }
        }

        // if (
        //   x.value == 'SitusCity' ||
        //   x.value == 'Fips' ||
        //   x.value == 'SitusZIP5'
        // ) {
        //   hasListBuyingCompulsoryField = true;
        // }
        return x;
      });
      // if (
      //   this.data?.moduleId == '67567bb418d5ea6344a6afee' &&
      //   !hasListBuyingCompulsoryField
      // ) {
      //   this._toastrService.error(
      //     'Please select County, City or Zip from filter.'
      //   );
      //   return;
      // }
    }

    const hasError = this.selectedFilterMain.some(
      (item) => item.error === true || item.operatorError === true
    );

    if (index == 2) {
      setTimeout(() => {
        this.searchElement?.nativeElement?.focus();
      }, 50);
    }

    if (hasError && index == 3) {
      return;
    } else if (hasError && index != 3) {
      this.tabIndex = index;
      this.step1 = index === 1;
      this.step2 = index === 2;
    } else {
      this.tabIndex = index;
      this.step1 = index === 1;
      this.step2 = index === 2;
      this.step3 = index === 3;
    }
  }

  async checkedValue(event, item, filter?) {
    if (event.target.checked) {
      let record = await this.commonFunctionCalls(this.filterParameters[item]);

      if (
        record.value === 'leadCustomQuestions' ||
        (record.value === 'buyerCustomQuestions' && this.isEdit)
      ) {
        if (filter != undefined) {
          record.masterOptions.forEach((x, index) => {
            filter[record.value].masterOptions.forEach((y) => {
              if (x.value.toString() === y.value.toString()) {
                record.masterOptions[index] = y;
              }
            });
          });
        } else {
          record.masterOptions.forEach((a, index) => {
            if (
              this.editFilterData?.leadCustomQuestions?.optionsType ==
                'QUESTION' ||
              this.editFilterData?.buyerCustomQuestions?.optionsType ==
                'QUESTION'
            ) {
              this.editFilterData[record.value].masterOptions.forEach((b) => {
                if (a.value.toString() === b.value.toString()) {
                  record.masterOptions[index] = b;
                }
              });
            }
          });
        }
      }

      this.filterParameters[item]['checked'] = true;

      this.filterParametersMain.map((c) => {
        if (c?.value == record?.value) {
          c['checked'] = true;
        }
        // if (record?.value == 'emailCampaign' && c.value == 'emailStats') {
        //   c.disabled = false;
        // }
        // if (record?.value == 'smsCampaign' && c.value == 'smsStats') {
        //   c.disabled = false;
        // }
        if (record?.value == 'targetZip') {
          c.selectedOption = [];
        }
        if (record?.value == 'targetCounty') {
          c.selectedOption = [];
        }
        // if(record?.value == 'targetStates'){
        //   c.selectedOption = []
        // }
        if (record?.value == 'targetCities') {
          c.selectedOption = [];
        }

        return c;
      });
      this.filterParameters.map((c) => {
        if (c?.value == record?.value) {
          c['checked'] = true;
        }
        // if (record?.value == 'emailCampaign' && c.value == 'emailStats') {
        //   c.disabled = false;
        // }
        // if (record?.value == 'smsCampaign' && c.value == 'smsStats') {
        //   c.disabled = false;
        // }
        if (record?.value == 'targetZip') {
          c.selectedOption = [];
        }
        if (record?.value == 'targetCounty') {
          c.selectedOption = [];
        }
        // if(record?.value == 'targetStates'){
        //   c.selectedOption = []
        // }
        if (record?.value == 'targetCities') {
          c.selectedOption = [];
        }
        return c;
      });

      let checkData = this.selectedFilter?.filter(
        (x) => x?.value == record.value
      );

      if (checkData.length == 0) {
        this.selectedFilter.push(record);
        this.selectedFilterMain.push(record);
      }

      this.selectedFilter = this.selectedFilter.sort(
        (x, y) => x.position - y.position
      );

      this.settleFilter();

      if (this.isEdit && filter) {
        this.getConditionType(filter);
      }
    }

    if (!event.target.checked) {
      this.filterParameters[item]['checked'] = false;
      this.filterParametersMain.map((c) => {
        if (c?.value == this.filterParameters[item]?.value) {
          c['checked'] = false;
        }
        // if (
        //   this.filterParameters[item]?.value == 'emailCampaign' &&
        //   c.value == 'emailStats'
        // ) {
        //   const checkChecked = c['checked'];
        //   c.disabled = true;
        //   c['checked'] = false;
        //   let checkExistingValue = this.selectedFilter.findIndex(
        //     (x: any) => x?.value == 'emailStats'
        //   );
        //   if (checkChecked == true) {
        //     this.selectedFilterMain.splice(checkExistingValue, 1);
        //   }
        // }
        // if (
        //   this.filterParameters[item]?.value == 'smsCampaign' &&
        //   c.value == 'smsStats'
        // ) {
        //   const checkChecked = c['checked'];
        //   c.disabled = true;
        //   c['checked'] = false;
        //   let checkExistingValue = this.selectedFilter.findIndex(
        //     (x: any) => x?.value == 'smsStats'
        //   );
        //   if (checkChecked == true) {
        //     this.selectedFilterMain.splice(checkExistingValue, 1);
        //   }
        // }
      });
      this.filterParameters.map((c) => {
        if (c?.value == this.filterParameters[item]?.value) {
          c['checked'] = false;
        }
        if (
          this.filterParameters[item]?.value == 'emailCampaign' &&
          c.value == 'emailStats'
        ) {
          const checkChecked = c['checked'];
          c.disabled = true;
          c['checked'] = false;

          let checkExistingValue = this.selectedFilter.findIndex(
            (x: any) => x?.value == 'emailStats'
          );
          if (checkChecked == true) {
            this.selectedFilter.splice(checkExistingValue, 1);
          }
        }
        if (
          this.filterParameters[item]?.value == 'smsCampaign' &&
          c.value == 'smsStats'
        ) {
          const checkChecked = c['checked'];
          c.disabled = true;
          c['checked'] = false;
          let checkExistingValue = this.selectedFilter.findIndex(
            (x: any) => x?.value == 'smsStats'
          );
          if (checkChecked == true) {
            this.selectedFilter.splice(checkExistingValue, 1);
          }
        }
      });

      let groupLabel = this.filterParameters[item];
      let checkExistingValue = this.selectedFilter.findIndex(
        (x: any) => x?.value == groupLabel?.value
      );

      let checkExistingValueMain = this.selectedFilterMain.findIndex(
        (x: any) => x?.value == groupLabel?.value
      );

      this.selectedFilter.splice(checkExistingValue, 1);
      // this.selectedFilterMain.splice(checkExistingValue, 1);
      this.selectedFilterMain.splice(checkExistingValueMain, 1);
      this.settleFilter(groupLabel);
    }
  }

  getConditionType(filter) {
    for (const key in filter) {
      this.selectedFilter.map((x) => {
        if (key == x?.value) {
          x.selectedCondition = filter[key]?.selectedCondition;
        }
      });
    }
    // Columns to Display Edit
    for (const key in filter) {
      this.selectedFilter.map((x) => {
        if (key == x?.value) {
          x.isVisible = filter[key]?.isVisible;
        }
      });
    }
  }

  settleFilter(item?) {
    if (item) {
      let unchecked = item;
      let columnsToDisplay = [];
      columnsToDisplay = this.selectedFilter.filter((x) => x?.isColumns);
      this.isColumnsToDisplay = columnsToDisplay.length > 0 ? true : false;

      if (unchecked?.isColumns) {
        let findGroup = this.selectedFilter.filter(
          (x) => x?.filterGroup == unchecked?.filterGroup
        );
        if (findGroup?.length == 0) {
          let index1 = this.selectedFilterShowGroup.findIndex(
            (item) => item.name === unchecked?.filterGroup
          );

          this.selectedFilterShowGroup.splice(index1, 1);
        }
      } else {
        let findGroup = this.selectedFilter.filter(
          (x) => x?.filterGroup == unchecked?.filterGroup
        );

        if (findGroup?.length == 0) {
          let index = this.selectedFilterGroup.findIndex(
            (item) => item.name === unchecked?.filterGroup
          );
          this.selectedFilterGroup.splice(index, 1);
        }
      }
    } else {
      let index;
      let index1;

      for (let i = 0; i < this.selectedFilter.length; i++) {
        this.selectedFilter[i]['show'] = 1;
        index = this.selectedFilterGroup.findIndex(
          (item) => item.name === this.selectedFilter[i]?.filterGroup
        );
        index1 = this.selectedFilterShowGroup.findIndex(
          (item) => item.name === this.selectedFilter[i]?.filterGroup
        );

        if (
          this.selectedFilter[i]?.filterGroup &&
          !this.selectedFilter[i]?.isColumns &&
          index < 0
        ) {
          this.selectedFilterGroup.push({
            name: this.selectedFilter[i]?.filterGroup,
            show: 1,
            position: this.selectedFilter[i]?.position,
          });
        }
        if (
          this.selectedFilter[i]?.filterGroup &&
          this.selectedFilter[i]?.isColumns &&
          index1 < 0
        ) {
          this.selectedFilterShowGroup.push({
            name: this.selectedFilter[i]?.filterGroup,
            show: 1,
            position: this.selectedFilter[i]?.position,
            isColumns: this.selectedFilter[i]?.isColumns ? true : false,
          });
        }

        this.isColumnsToDisplay = this.selectedFilter[i]?.isColumns
          ? true
          : false;
      }

      this.selectedFilterGroup = this.selectedFilterGroup.sort(
        (x, y) => x.position - y.position
      );
      this.selectedFilterShowGroup = this.selectedFilterShowGroup.sort(
        (a, b) => a.position - b.position
      );
    }
  }

  continueToNext() {
    let hasListBuyingCompulsoryField = false;
    this.selectedFilterMain.map(async (x) => {
      if (this.tabIndex == 1) {
        x.error = false;
        x.operatorError = false;
        x.errorStartDate = false;
        x.errorEndDate = false;
        x.errorMinVal = false;
        x.errorMaxVal = false;
        x.conditionError = false;
      }

      //for select
      if (x.optionsType === 'SELECT' && this.tabIndex === 2) {
        x.error = !x.selectedOption || x.selectedOption.length === 0;
      }

      //for operator
      if (!x?.selectedOperator && this.tabIndex === 2) {
        x.operatorError = true;
      } else {
        x.operatorError = false;
      }

      //for date range
      if (x.optionsType === 'DATE-RANGE' && this.tabIndex === 2) {
        if (x?.selectedOperator != 'is') {
          // For start date
          if (!x.minVal) {
            x.error = x.errorStartDate = true;
            x.rangeErrorMsg = 'Please add Date';
          } else {
            x.errorStartDate = false;
            if (x.selectedOperator !== 'between') {
              x.error = false;
              x.rangeErrorMsg = '';
            }
          }

          // For end date
          if (!x.maxVal && x.selectedOperator === 'between') {
            x.error = x.errorEndDate = true;
            x.rangeErrorMsg = 'Please add Date';
          } else {
            x.errorEndDate = false;
            if (x.minVal) {
              x.error = false;
              x.rangeErrorMsg = '';
            }
          }
        }
      }

      //for range
      if (x.optionsType === 'RANGE' && this.tabIndex === 2) {
        // For min value
        if (
          x.minVal == null ||
          x.minVal == undefined ||
          x.minVal == '' ||
          !x.noMinValidatation
        ) {
          x.error = x.errorMinVal = true;
          x.rangeErrorMsg = 'Please add value';
        } else {
          x.errorMinVal = false;
          if (x.selectedOperator !== 'between') {
            x.error = false;
            x.rangeErrorMsg = '';
          }
        }

        // For max value
        if (!x.maxVal && x.selectedOperator === 'between') {
          x.error = x.errorMaxVal = true;
          x.rangeErrorMsg = 'Please add value';
        } else {
          x.errorMaxVal = false;
          if (x.minVal) {
            x.error = false;
            x.rangeErrorMsg = '';
          }
        }

        // For value error
        if (x.minVal && x.maxVal && x.minVal >= x.maxVal) {
          x.error = x.errorMaxVal = true;
          x.rangeErrorMsg = 'Max. value less than or equal to Min. value';
        }
      }

      // For time range and multi-select
      if (
        (x.optionsType === 'TIME-RANGE' ||
          x.optionsType === 'TIME-RANGE-ZERO' ||
          x.optionsType === 'MULTI-SELECT' ||
          x.optionsType === 'MULTI-SELECT-TITLE') &&
        this.tabIndex === 2
      ) {
        if (
          !x.selectedOption ||
          x.selectedOption == '' ||
          x.selectedOption.length == 0
        ) {
          x.error = x.errorMinVal = true;
          x.rangeErrorMsg = 'Please add value';
        } else {
          x.error = x.errorMinVal = false;
          x.rangeErrorMsg = '';
        }

        if (
          x.optionsType === 'TIME-RANGE' ||
          x.optionsType === 'TIME-RANGE-ZERO'
        ) {
          x.error = x.conditionError = false;
          x.rangeErrorMsg = '';
          if (!x.selectedCondition) {
            x.error = x.conditionError = true;
            x.rangeErrorMsg = 'Please add value';
          }
          if (!x.selectedOption) {
            x.error = x.errorMinVal = true;
            x.rangeErrorMsg = 'Please add value';
          }
        }
      }

      if (x.optionsType === 'DATE-SCORE' && this.tabIndex === 2) {
        if (!x.minVal) {
          x.error = x.errorStartDate = true;
          x.rangeErrorMsg = 'Please add Date';
        } else {
          x.errorStartDate = false;
          x.error = false;
          x.rangeErrorMsg = '';
        }
      }

      //for custom questions
      if (
        (x.value == 'leadCustomQuestions' && this.tabIndex !== 1) ||
        (x.value == 'buyerCustomQuestions' && this.tabIndex === 2)
      ) {
        const hasFinalError = x.masterOptions.filter(
          (item) => !item.selectedOption && !item.minVal && !item.maxVal
        );

        if (hasFinalError.length == x.masterOptions.length) {
          x.error = true;
          this._toastrService.error(
            this.messageConstant.quickFilterSelectField
          );
        } else {
          x.error = false;
        }
      }

      // if (x.selectedOperator == 'is' && x.optionsType === 'DATE-RANGE') {
      //   x = await this.changeConditionalDateRange(x);
      // }
      if (
        x.optionsType === 'TIME-RANGE' ||
        x.optionsType === 'TIME-RANGE-ZERO'
      ) {
        x = await this.changeConditionalTimeRange(x);
      }

      // if (
      //   x.value == 'SitusCity' ||
      //   x.value == 'Fips' ||
      //   x.value == 'SitusZIP5'
      // ) {
      //   hasListBuyingCompulsoryField = true;
      // }

      // Columns to Display
      if (x?.isColumns && this.tabIndex === 2) {
        delete x.conditionError;
        delete x.error;
        delete x.errorEndDate;
        delete x.errorMaxVal;
        delete x.errorMinVal;
        delete x.errorStartDate;
        delete x.operatorError;
      }

      return x;
    });

    const hasError = this.selectedFilterMain.some(
      (item) => item.error === true || item.operatorError === true
    );

    // if (
    //   this.data?.moduleId == '67567bb418d5ea6344a6afee' &&
    //   !hasListBuyingCompulsoryField
    // ) {
    //   this._toastrService.error(
    //     'Please select County, City or Zip from filter.'
    //   );
    //   return;
    // }
    if (!hasError) {
      if (this.tabIndex < 3) {
        this.tabIndex += 1;
      }
      this.tabChange(this.tabIndex);
    } else {
      return;
    }
  }

  // changeConditionalDateRange(filter) {
  //   var currentDate = moment();
  //   switch (filter?.selectedCondition) {
  //     case 'today':
  //       let today = moment().startOf('day');
  //       filter.minVal = today.valueOf();
  //       filter.maxVal = moment(today).endOf('day').valueOf();
  //       break;
  //     case 'week':
  //       filter.minVal = moment().startOf('isoWeek').valueOf();
  //       filter.maxVal = moment().endOf('isoWeek').valueOf();
  //       break;
  //     case 'weekToDate':
  //       filter.minVal = moment().startOf('isoWeek').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'month':
  //       filter.minVal = moment().startOf('month').valueOf();
  //       filter.maxVal = moment().endOf('month').valueOf();
  //       break;
  //     case 'monthToDate':
  //       filter.minVal = moment().startOf('month').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'quarter':
  //       filter.minVal = moment().startOf('quarter').valueOf();
  //       filter.maxVal = moment().endOf('quarter').valueOf();
  //       break;
  //     case 'quarterToDate':
  //       filter.minVal = moment().startOf('quarter').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'year':
  //       filter.minVal = moment().startOf('year').valueOf();
  //       filter.maxVal = moment().endOf('year').valueOf();
  //       break;
  //     case 'yearToDate':
  //       filter.minVal = moment().startOf('year').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'yesterday':
  //       let yesterday = moment().subtract(1, 'days').startOf('day');
  //       filter.minVal = yesterday.valueOf();
  //       filter.maxVal = moment(yesterday).endOf('day').valueOf();
  //       break;
  //     case 'lastWeek':
  //       filter.minVal = moment()
  //         .subtract(1, 'week')
  //         .startOf('isoWeek')
  //         .valueOf();
  //       filter.maxVal = moment().subtract(1, 'week').endOf('isoWeek').valueOf();
  //       break;
  //     case 'lastWeekToDate':
  //       filter.minVal = moment()
  //         .subtract(1, 'week')
  //         .startOf('isoWeek')
  //         .valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'lastMonth':
  //       filter.minVal = moment()
  //         .subtract(1, 'month')
  //         .startOf('month')
  //         .valueOf();
  //       filter.maxVal = moment().subtract(1, 'month').endOf('month').valueOf();
  //       break;
  //     case 'lastMonthToDate':
  //       filter.minVal = moment()
  //         .subtract(1, 'month')
  //         .startOf('month')
  //         .valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'lastQuarter':
  //       filter.minVal = moment()
  //         .subtract(1, 'quarter')
  //         .startOf('quarter')
  //         .valueOf();
  //       filter.maxVal = moment()
  //         .subtract(1, 'quarter')
  //         .endOf('quarter')
  //         .valueOf();
  //       break;
  //     case 'lastQuarterToDate':
  //       filter.minVal = moment()
  //         .subtract(1, 'quarter')
  //         .startOf('quarter')
  //         .valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'lastYear':
  //       filter.minVal = moment().subtract(1, 'year').startOf('year').valueOf();
  //       filter.maxVal = moment().subtract(1, 'year').endOf('year').valueOf();
  //       break;
  //     case 'lastYearToDate':
  //       filter.minVal = moment().subtract(1, 'year').startOf('year').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'last7Days':
  //       filter.minVal = moment().subtract(6, 'days').startOf('day').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'last14Days':
  //       filter.minVal = moment().subtract(13, 'days').startOf('day').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'last30Days':
  //       filter.minVal = moment().subtract(29, 'days').startOf('day').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'last60Days':
  //       filter.minVal = moment().subtract(59, 'days').startOf('day').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'last90Days':
  //       filter.minVal = moment().subtract(89, 'days').startOf('day').valueOf();
  //       filter.maxVal = moment(currentDate).endOf('day').valueOf();
  //       break;
  //     case 'nextWeek':
  //       filter.minVal = moment().add(1, 'week').startOf('isoWeek').valueOf();
  //       filter.maxVal = moment().add(1, 'week').endOf('isoWeek').valueOf();
  //       break;
  //     case 'next2Weeks':
  //       filter.minVal = moment().add(1, 'week').startOf('isoWeek').valueOf();
  //       filter.maxVal = moment().add(2, 'week').endOf('isoWeek').valueOf();
  //       break;
  //     case 'next4Weeks':
  //       filter.minVal = moment().add(1, 'week').startOf('isoWeek').valueOf();
  //       filter.maxVal = moment().add(4, 'week').endOf('isoWeek').valueOf();
  //       break;
  //     case 'nextMonth':
  //       filter.minVal = moment().add(1, 'month').startOf('month').valueOf();
  //       filter.maxVal = moment().add(1, 'month').endOf('month').valueOf();
  //       break;
  //     case 'nextQuarter':
  //       filter.minVal = moment().add(1, 'quarter').startOf('quarter').valueOf();
  //       filter.maxVal = moment().add(1, 'quarter').endOf('quarter').valueOf();
  //       break;
  //     case 'nextYear':
  //       filter.minVal = moment().add(1, 'year').startOf('year').valueOf();
  //       filter.maxVal = moment().add(1, 'year').endOf('year').valueOf();
  //       break;
  //     default:
  //       break;
  //   }
  //   return filter;
  // }

  changeConditionalTimeRange(filter) {
    let finalDays = filter?.selectedOption - 1;

    switch (filter?.value) {
      case 'mlsMarket':
        if (filter?.selectedOperator == 'is') {
          let today = moment().startOf('day');
          filter.minVal = today.valueOf();
          filter.maxVal = moment()
            .add(finalDays, 'days')
            .endOf('day')
            .valueOf();
        }
        if (filter?.selectedOperator == 'morethan') {
          filter.minVal = moment()
            .add(filter?.selectedOption, 'days')
            .startOf('day')
            .valueOf();
        }
        if (filter?.selectedOperator == 'lessthan') {
          filter.minVal = moment()
            .add(filter?.selectedOption, 'days')
            .startOf('day')
            .valueOf();
        }
        break;

      default:
        break;
    }

    return filter;
  }

  async applyFilter() {
    let { filter }: { filter: any; isValid: boolean } = this.calculateFilter(
      this.selectedFilterMain
    );

    if (this.data?.isReturnFilterData) {
      this.isSubmitted = true;
      if (this.filterDetailForm.invalid) return;

      const { filterTitle, filterDescription } = this.filterDetailForm.value;
      const data = {
        title: filterTitle,
        description: filterDescription,
        filterData: filter,
      };

      this.dialog.close({ data });
      return;
    }

    await this.saveFilter(filter);
  }

  calculateFilter(filter) {
    let obj = {};
    let isValid = true;

    filter.map(async (x) => {
      let masterOptions;
      if (x.masterOptions && x.masterOptions.length > 0) {
        masterOptions = await this.masterOptionsFilter(x.masterOptions);
      }

      if (x?.optionsType == 'RANGE' && x.isPrice) {
        if (x.minVal) {
          x.minVal = parseFloat(x.minVal);
        }
        if (x.maxVal) {
          x.maxVal = parseFloat(x.maxVal);
        }
      }

      let value = x.selectedOption;
      let dateValue = null;
      obj[x.value] = {
        value,
        operator: x.selectedOperator,
        optionsType: x.optionsType,
        order: x.order,
        minVal: x.minVal,
        maxVal: x.maxVal,
        dateValue,
        masterOptions: masterOptions,
      };
      if (
        x?.optionsType == 'DATE-RANGE' ||
        x?.optionsType == 'TIME-RANGE' ||
        x?.optionsType == 'TIME-RANGE-ZERO'
      ) {
        delete obj['value'];
        if (x.minVal) {
          let timezoneoffset =
            this._commonFunctionService.dueDateFormat().browserTimezone;

          const utcOffset = moment(new Date()).tz(timezoneoffset).format('Z');
          let ak = moment(new Date(x.minVal)).format('YYYY-MM-DD');
          let startDate1 = new Date(ak + 'T00:00:00.000Z');
          let startDate11 = new Date(startDate1).getTime();
          let newStartDate =
            startDate11 + startDate1.getTimezoneOffset() * 60 * 1000;

          obj[x.value]['minVal'] = newStartDate;
          obj[x.value]['selectedStartDate'] = x.minVal;
        }

        if (x.maxVal) {
          let timezoneoffset =
            this._commonFunctionService.dueDateFormat().browserTimezone;
          let bk = moment(new Date(x.maxVal)).format('YYYY-MM-DD');
          let endDate1 = new Date(bk + 'T23:59:59.000Z');
          let endDate11 = new Date(endDate1).getTime();
          let newEndDate = endDate11 + endDate1.getTimezoneOffset() * 60 * 1000;

          obj[x.value]['maxVal'] = newEndDate;
          obj[x.value]['selectedEndDate'] = x.maxVal;
        }
      }
      if (x?.selectedCondition) {
        obj[x.value]['selectedCondition'] = x?.selectedCondition;
      }
      if (x?.isColumns) {
        obj[x.value] = {
          isColumns: x?.isColumns,
          isVisible: x?.isVisible,
          label: x?.label,
          value: x?.value,
        };
      }
      if (x?.optionsType == 'DATE-SCORE') {
        delete obj['value'];

        if (x.minVal) {
          let timezoneoffset =
            this._commonFunctionService.dueDateFormat().browserTimezone;

          const utcOffset = moment(new Date()).tz(timezoneoffset).format('Z');
          let ak = moment(new Date(x.minVal)).format('YYYY-MM-DD');

          // Start of day
          let startDate1 = new Date(ak + 'T00:00:00.000Z');
          let startDate11 = new Date(startDate1).getTime();
          let newStartDate =
            startDate11 + startDate1.getTimezoneOffset() * 60 * 1000;

          // End of day
          let endDate1 = new Date(ak + 'T23:59:59.999Z');
          let endDate11 = new Date(endDate1).getTime();
          let newEndDate = endDate11 + endDate1.getTimezoneOffset() * 60 * 1000;

          obj[x.value]['minVal'] = newStartDate;
          obj[x.value]['selectedStartDate'] = x.minVal;

          obj[x.value]['maxVal'] = newEndDate;
        }
      }

      return x;
    });

    return { filter: obj, isValid };
  }

  masterOptionsFilter(masterData) {
    let data = masterData.filter(
      (x) => (x.selectedOption || x.minVal) && x.selectedOption != ''
    );
    return data;
  }

  saveFilter(data) {
    // if (
    //   this.data?.moduleId == '67567bb418d5ea6344a6afee' &&
    //   !data?.Fips &&
    //   !data?.SitusCity &&
    //   !data?.SitusZIP5
    // ) {
    //   this._toastrService.error(
    //     'Please select County, City or Zip from filter.'
    //   );
    //   return;
    // }
    if (this.isSaveFilter) {
      this.isSubmitted = true;
      if (this.userPermission.length == 0) {
        return;
      }
      if (this.filterDetailForm.invalid) {
        return;
      }
      let filterData = [data];
      let { filterTitle, filterDescription } = this.filterDetailForm.value;
      let obj = {
        title: filterTitle,
        description: filterDescription,
        quickFilterType: 2,
        moduleId: this.data.moduleId,
        accessingUser: this.userPermission,
      };

      setTimeout(() => {
        obj['filterData'] = JSON.stringify(filterData);
      }, 500);

      if (Object.keys(this.selectedFolderData).length > 0) {
        obj['folderId'] = this.selectedFolderData?._id;
      } else {
        return;
      }

      this._commonFunctionService.leadFilterName = filterTitle;

      this._loaderService.start();

      if (!this.isEdit) {
        setTimeout(() => {
          this._masterFilterService.saveMasterFilter(obj).subscribe(
            (response: ResponseModel) => {
              this._loaderService.stop();
              if (response.statusCode && response.statusCode == 200) {
                this._sharedService.refreshMasterFilter.next(true);
                this.dialog.close({ data, activatedFilter: response?.data });
                this._toastrService.success(
                  this.messageConstant?.addFilterSuccess,
                  ''
                );
              } else {
                this._toastrService.error(response.message, '');
              }
            },
            (err: ErrorModel) => {
              this._loaderService.stop();
              if (err.error) {
                const error: ResponseModel = err.error;
                this._toastrService.error(error.message, '');
              } else {
                this._toastrService.error(
                  this.messageConstant.unknownError,
                  ''
                );
              }
            }
          );
        }, 1000);
      } else {
        setTimeout(() => {
          obj['quickFilterId'] = this.data?.filterRecord?._id;
          obj['position'] = this.data?.filterRecord?.position;
          if (this.data?.isFolder) {
            obj['isFolder'] = this.data?.isFolder;
          }

          this._masterFilterService.editMasterFilter(obj).subscribe(
            (response: ResponseModel) => {
              this._loaderService.stop();
              if (response.statusCode && response.statusCode == 200) {
                this._sharedService.refreshMasterFilter.next(true);
                this.dialog.close({ data, activatedFilter: response?.data });
                this._toastrService.success(
                  this.messageConstant?.editFilterSuccess,
                  ''
                );
              } else {
                this._toastrService.error(response.message, '');
              }
            },
            (err: ErrorModel) => {
              this._loaderService.stop();
              if (err.error) {
                const error: ResponseModel = err.error;
                this._toastrService.error(error.message, '');
              } else {
                this._toastrService.error(
                  this.messageConstant.unknownError,
                  ''
                );
              }
            }
          );
        }, 1000);
      }
    } else {
      this._commonFunctionService.leadFilterName = this.data.filterRecord?.title
        ? this.data.filterRecord?.title
        : 'N/A';
      this.dialog.close({ data });
    }
  }

  searchFiltersOne(): void {
    let filterArray = JSON.parse(JSON.stringify(this.filterParametersMain));

    if (this.isIndividualDashboard || this.isComparisonDashboard) {
      filterArray = filterArray.filter((f) => !f.isColumns);
    }
    this.groupingFilter(filterArray);

    const filtered = filterArray.filter((filter) =>
      filter.label.toLowerCase().includes(this.fieldFind.toLowerCase())
    );

    let findGroup = [];
    let findShowGroup = [];
    const finalFindGroup = [
      ...new Set(
        filtered
          .map((filter) => {
            if (filter.filterGroup && !filter?.isColumns) {
              return filter.filterGroup;
            }
          })
          .filter((group) => group !== undefined)
      ),
    ];

    finalFindGroup.map((x) => {
      let obj = {
        name: x,
        show: 1,
      };
      findGroup.push(obj);
    });
    const finalFindShowGroup = [
      ...new Set(
        filtered
          .map((filter) => {
            if (filter.filterGroup && filter?.isColumns) {
              return filter.filterGroup;
            }
          })
          .filter((group) => group !== undefined)
      ),
    ];

    finalFindShowGroup.map((x) => {
      let obj = {
        name: x,
        show: 1,
      };
      findShowGroup.push(obj);
    });

    this.filterParameters = filtered;
    this.filterGroup = findGroup;
    this.filterShowGroup = findShowGroup;
  }

  searchFiltersTwo() {
    this.selectedFilterMain.map((x) => {
      this.selectedFilter.map((y) => {
        if (y.label == x.label) {
          x = y;
        }
      });
      return x;
    });

    let filterArray = this.selectedFilterMain;
    let findGroup = [];
    let findShowGroup = [];
    const filtered = filterArray.filter((filter) =>
      filter.label.toLowerCase().includes(this.selectFieldFind.toLowerCase())
    );
    const finalFindGroup = [
      ...new Set(
        filtered
          .map((filter) => {
            if (!filter?.isColumns) {
              return filter.filterGroup;
            }
          })
          .filter((group) => group !== undefined)
      ),
    ];

    finalFindGroup.map((x) => {
      let obj = {
        name: x,
        show: 1,
      };
      findGroup.push(obj);
    });

    const finalFindShowGroup = [
      ...new Set(
        filtered
          .map((filter) => {
            if (filter?.isColumns) {
              return filter.filterGroup;
            }
          })
          .filter((group) => group !== undefined)
      ),
    ];

    finalFindShowGroup.map((x) => {
      let obj = {
        name: x,
        show: 1,
      };
      findShowGroup.push(obj);
    });

    this.selectedFilter = filtered;
    this.selectedFilterGroup = findGroup;
    this.selectedFilterShowGroup = findShowGroup;
  }

  manageAction(action) {
    this.isSaveFilter = false;
    switch (action) {
      case 'share':
        this.isShare = true;
        setTimeout(() => {
          this.searchElement?.nativeElement?.focus();
        }, 50);
        break;

      default:
        break;
    }
  }

  backToApplyFilterMenu() {
    this.isShare = false;
    this.isSaveFilter = true;
  }

  hasBothSelectTypes(filter: any): boolean {
    return (
      filter.optionsType === 'MULTI-SELECT' &&
      filter.conditionType &&
      filter.conditionType.length > 0 &&
      filter.operator &&
      filter.operator.length > 0
    );
  }

  backToApplyFilter() {
    if (this.userData?._id != undefined) {
      this.userPermission = this.userPermissionMain.map((item) => ({
        userId: item?._id,
        permission: item?.permission,
      }));

      this.isUserView = this.userPermission.some(
        (record) => record.permission === 1
      );

      this.isSaveFilter = true;
      this.isShare = false;
    }
  }

  getSubUserList() {
    let param = {
      page: 1,
      limit: 1000,
    };

    this._userService.getSubUserList(param).subscribe(
      (response: ResponseModel) => {
        if (response?.statusCode == 200) {
          this.allMemberList = response?.data?.items;

          // this.userPermissionMain = [
          //   {
          //     _id: this._commonFunctionService.userData?._id,
          //     name:
          //       this._commonFunctionService.userData?.firstName +
          //       ' ' +
          //       this._commonFunctionService.userData?.lastName,
          //     permission: 1,
          //   },
          // ];

          this.userList = this.allMemberList.map((item) => ({
            _id: item._id,
            name: item.name,
            permission:
              item?._id.toString() == this.userData?._id.toString() ? 1 : 3,
            profileImage: item.profileImage,
            createdAt: item.createdAt,
          }));

          this.userListMain = this.allMemberList.map((item) => ({
            _id: item._id,
            name: item.name,
            permission:
              item?._id.toString() == this.userData?._id.toString() ? 1 : 3,
            profileImage: item.profileImage,
            createdAt: item.createdAt,
          }));

          if (this.isEdit) {
            let editUserPermisson = [];
            this.userList.map((y) => {
              this.data.filterRecord.accessingUser.map((x) => {
                if (y?._id?.toString() == x?.userId?.toString()) {
                  y.permission = x.permission;
                  let obj = {
                    _id: x?.userId,
                    permission: x.permission,
                    name: y.name,
                    profileImage: y.profileImage,
                  };
                  editUserPermisson.push(obj);
                }
              });
            });
            this.userListMain.map((y) => {
              this.data.filterRecord.accessingUser.map((x) => {
                if (y?._id?.toString() == x?.userId?.toString()) {
                  y.permission = x.permission;
                }
              });
            });

            this.userPermissionMain = editUserPermisson;
            this.userPermission = this.data.filterRecord.accessingUser;
            this.isUserView = this.userPermission?.length > 0 ? true : false;
          }
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
        if (err.error) {
          const error: ResponseModel = err.error;
          this._toastrService.error(error.message, '');
        } else {
          this._toastrService.error(this.messageConstant.unknownError, '');
        }
      }
    );
  }

  getInitials(user) {
    let initials = user.match(/\b\w/g) || [];
    return ((initials.shift() || '') + (initials.pop() || '')).toUpperCase();
  }

  // checkedUser(event, data) {
  //   this.userListMain.map((x) => {
  //     if (x?._id == data?._id) {
  //       x.permission = event?.value;
  //     }
  //     return x;
  //   });
  // }

  // searchFiltersThree(event) {
  //   this.userList = this.userListMain.filter((filter) =>
  //     filter.name.toLowerCase().includes(event?.query.toLowerCase())
  //   );
  // }

  checkedUser(event, data) {
    this.userListMain.map((x) => {
      if (x?._id == data?._id) {
        x.permission = event?.value;
        if (event?.value == 3) {
          let checkExistingValue = this.userPermissionMain.findIndex(
            (x: any) => x?._id == data?._id
          );
          this.userPermissionMain.splice(checkExistingValue, 1);
        } else {
          let checkExistingValue = this.userPermissionMain.findIndex(
            (x: any) => x?._id == data?._id
          );
          if (checkExistingValue >= 0) {
            this.userPermissionMain.splice(checkExistingValue, 1);
          }
          this.userPermissionMain.push(x);
        }
      }
      return x;
    });
  }

  searchFiltersThree() {
    this.userList = this.userListMain.filter((filter) =>
      filter.name.toLowerCase().includes(this.userFind.toLowerCase())
    );
  }

  selectUser(event) {
    let userList = this.userPermissionMain.filter((filter) =>
      filter.name.toLowerCase().includes(event.name.toLowerCase())
    );

    if (userList.length == 0) {
      this.userPermissionMain.push(event);
    }
    this.userFind = '';
  }

  showMenu() {
    this.isMenu = !this.isMenu;
    setTimeout(() => {
      this.searchElement?.nativeElement?.focus();
    }, 50);
  }

  searchFiltersFour() {
    this.folderList = this.folderListMain.filter((filter) =>
      filter.title.toLowerCase().includes(this.fieldFolder.toLowerCase())
    );
  }

  selectFolder(event) {
    this.selectedFolderData = event;
    this.isMenu = false;
    this.fieldFolder = '';
    this.folderList = JSON.parse(JSON.stringify(this.folderListMain));
  }

  isObjectEmpty(obj: any): boolean {
    return Object.keys(obj).length === 0;
  }

  deleteUser(item) {
    let index = this.userPermissionMain.indexOf(item);
    if (index > 0) {
      this.userPermissionMain.splice(index, 1);
    }
  }

  async commonFunctionCalls(filter: any) {
    let filterCopy = Object.assign({}, filter);

    switch (filter.value) {
      case 'marketId':
        filterCopy.options = await this.getMarket();
        break;
      // case 'roleIds':
      //   filterCopy.options = await this.getRoles();
      //   break;
      case 'teamMembers':
      case 'assignUserIds':
      case 'taskAssignee':
        filterCopy.options = this.mainUserList;
        break;
      case 'teamAssignee':
        filterCopy.options = await this.getUser();
        break;
      case 'tagIds':
        filterCopy.options = await this.getAllTags();
        break;
      case 'leadExit':
        filterCopy.options = await this.getProjectType();
        break;
      case 'crmQuestionId':
        filterCopy.options = await this.getLeadSource();
        break;
      case 'numberId':
        filterCopy.options = await this.getCampaignList();
        break;
      case 'mainStatus':
        filterCopy.options = await this.getMainStatus();
        break;
      case 'propertyType':
        filterCopy.options = await this.getPropertyType();
        break;
      case 'numberType':
        filterCopy.options = this.numberTypeList;
        break;
      // case 'dripCampaignsInclude':
      case 'dripCampaign':
        filterCopy.options = await this.onDripList();
        break;
      case 'taskName':
        filterCopy.options = await this.taskNameList(false);
        break;
      case 'taskType':
        filterCopy.options = await this.taskTypeList();
        break;
      case 'apptStatus':
        filterCopy.options = MiscellaneousConstant.appointmentStatus;
        break;
      // case 'city':
      //   filterCopy.options = await this.leadDecomposeAddress('city');
      //   break;
      case 'Property_City_Code':
        filterCopy.options = await this.leadDecomposeAddress(
          'leadInfo.Property_City'
        );
        break;
      // case 'state':
      //   filterCopy.options = await this.leadDecomposeAddress('state');
      //   break;
      case 'Property_State_Code':
        filterCopy.options = await this.leadDecomposeAddress(
          'leadInfo.Property_State'
        );
        break;
      // case 'zip':
      //   filterCopy.options = await this.leadDecomposeAddress('zip');
      //   break;
      case 'Property_Zip_Code':
        filterCopy.options = await this.leadDecomposeAddress(
          'leadInfo.Property_Zip_Code'
        );
        break;
      case 'county':
        filterCopy.options = await countyConstant.countyArray;
        break;
      case 'ownerMailingCity':
        filterCopy.options = await this.leadDecomposeAddress(
          'ownerMailingCity'
        );
        break;
      case 'ownerMailingState':
        filterCopy.options = await this.leadDecomposeAddress(
          'ownerMailingState'
        );
        break;
      case 'ownerMailingZip':
        filterCopy.options = await this.leadDecomposeAddress('ownerMailingZip');
        break;
      case 'ownerMailingCounty':
        filterCopy.options = await countyConstant.countyArray;
        break;
      case 'leadCustomQuestions':
        let moduleId = this.data?.moduleId;
        if (moduleId == StatusConstant.MainStatusId.WHOLESALEPIPELINE) {
          moduleId = StatusConstant.MainStatusId.LEAD;
        }
        filterCopy.masterOptions = await this.getGroups(moduleId);
        break;
      case 'deadResons':
        filterCopy.options = await this.getDeadReasons();
        break;
      case 'outgoingCallResult':
        filterCopy.options = await this.getOutgoingCallResult();
        break;
      // Buyer Filter Cases
      case 'targetCities':
        // filterCopy.options = await this.getTargetList(2, '');
        filterCopy.options = await this.getBuyerCitiesAndZips('city');
        break;
      case 'targetZip':
        // filterCopy.options = await this.getTargetList(4, '');
        filterCopy.options = await this.getBuyerCitiesAndZips('zip');
        break;
      case 'targetStates':
        // filterCopy.options = await this.getTargetList(3, '');
        filterCopy.options = await this.getBuyerCitiesAndZips('States');
        break;
      case 'targetCounty':
        // filterCopy.options = await this.getTargetList(1, '');
        filterCopy.options = await this.getBuyerCitiesAndZips('Counties');
        break;
      case 'tags':
        filterCopy.options = await this.getAllTags();
        this.getUsedColorIndex();
        break;
      case 'emailCampaign':
        filterCopy.options = await this.getCampaigns({}, 'email');
        break;
      case 'smsCampaign':
        filterCopy.options = await this.getCampaigns({}, 'sms');
        break;
      case 'buyerCustomQuestions':
        let buyerModuleId = this.data?.moduleId;
        filterCopy.masterOptions = await this.getGroups(buyerModuleId);
        break;
      //Vendor Filter Cases
      case 'vendorType':
        filterCopy.options = await this.getVendorCategory();
        break;
      case 'targetAreas':
        filterCopy.options = await this.getVendorTags();
        break;
      //Vendor Filter Over
      case 'unAssigned':
        filterCopy.options = await this.getRoles();
        break;
      //List stacking filters
      case 'Property_House_Type':
        filterCopy.options = await this.getPropertyType();
        break;
      case 'SchoolDistrictName':
        filterCopy.options = await this.getAddressFilter('schoolDistrictNames');
        break;
      case 'Fips':
        filterCopy.options = await this.getAddressFilter('counties');
        break;
      case 'SitusCity':
        filterCopy.options = await this.getAddressFilter('cities');
        break;
      case 'SitusZIP5':
        filterCopy.options = await this.getAddressFilter('zip');
        break;
      case 'owners':
        filterCopy.options = await this.getUser();
        break;
      case 'PoolCode':
        filterCopy.options = await this.getPools();
        break;
      case 'Garage':
        filterCopy.options = await this.getGarage();
        break;
      case 'BasementCode':
        filterCopy.options = await this.getBasement();
        break;
      default:
        break;
    }

    //Condition type add to filter
    if (filterCopy?.optionsType == 'DATE-RANGE') {
      filterCopy.conditionType = this.conditionalDate;
      filterCopy.selectedCondition = this.conditionalDate[0]?.value;
    }
    if (
      (filterCopy?.optionsType == 'TIME-RANGE' ||
        filterCopy?.optionsType == 'TIME-RANGE-ZERO') &&
      filter.value != 'mlsMarket' &&
      filter.value != 'ownershipDuration' &&
      filter?.value != 'incomingCallDuration' &&
      filter?.value != 'outgoingCallDuration' &&
      filter?.value != 'openLeadDuration' &&
      filter?.value != 'holding'
    ) {
      filterCopy.conditionType = this.conditionalTime;
      filterCopy.selectedCondition = this.conditionalTime[0]?.value;
    }

    if (filterCopy.filterGroup == 'Team' && filter.value != 'unAssigned') {
      let roleId = RoleObject.roles[filterCopy.value];
      filterCopy.options = await this.getUserRole(roleId);
    }

    return filterCopy;
  }

  getMarket() {
    return new Promise((resolve, reject) => {
      this._loaderService.start();
      const obj = {
        page: 1,
        limit: 1000,
      };

      this._marketServices.getMarketsV2(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let arr = response.data.items;
            const result = arr.reduce((acc, d) => {
              if (d.title) {
                const value = { _id: d?._id, label: d?.title, value: d?._id };
                acc.push(value);
              }
              return acc;
            }, []);

            this.marketList = result;
            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getRoles() {
    return new Promise((resolve, reject) => {
      let param = {};
      this._loaderService.start();

      this._userService.getRoles(param).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let role = response?.data.sort((a, b) => {
              return a.name.localeCompare(b.name);
            });
            const result = role.reduce((acc, d) => {
              if (d.name) {
                const value = { _id: d?._id, label: d?.name, value: d?._id };
                acc.push(value);
              }
              return acc;
            }, []);
            this.roleList = result;
            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getUser() {
    return new Promise((resolve, reject) => {
      let param = {};
      this._loaderService.start();

      this._userService.getUsers(param).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let user = response?.data;

            const result = user.reduce((acc, d) => {
              const value = {
                _id: d._id,
                label: d?.firstName + ' ' + d?.lastName,
                value: d?._id,
              };
              acc.push(value);

              return acc;
            }, []);

            this.mainUserList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getUserRole(roleId) {
    return new Promise((resolve, reject) => {
      let param = {};
      this._loaderService.start();

      this._userService.getUsers(param).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let user = response?.data;

            let finalRole = user.filter((record) =>
              record.roleId.includes(roleId)
            );

            const result = finalRole.reduce((acc, d) => {
              const value = {
                _id: d._id,
                label: d?.firstName + ' ' + d?.lastName,
                value: d?._id,
              };
              acc.push(value);

              return acc;
            }, []);

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getAllTags() {
    return new Promise((resolve, reject) => {
      this.tagList = [];
      this._loaderService.start();

      this._taggingService.getTags(this.tagType).subscribe(
        (response) => {
          this._loaderService.stop();

          if (response && response.statusCode == 200) {
            let user = response?.data;

            const result = user.reduce((acc, d) => {
              if (d.label) {
                const value = { _id: d?._id, label: d?.label, value: d?._id };
                acc.push(value);
              }
              return acc;
            }, []);

            this.tagList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getProjectType() {
    return new Promise((resolve, reject) => {
      let obj = {
        page: 1,
      };
      this.projectTypeList = [];
      this._loaderService.start();

      this._inventoryService.getInvestmentType(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let projectType = response?.data?.items;
            const result = projectType.reduce((acc, d) => {
              if (d.name) {
                const value = { _id: d?._id, label: d?.name, value: d?._id };
                acc.push(value);
              }
              return acc;
            }, []);

            this.projectTypeList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getLeadSource() {
    return new Promise((resolve, reject) => {
      const obj = {
        searchString: 'hear-about-us',
      };

      this.crmQuestionList = [];
      this._loaderService.start();

      this._leadsService.getCrmQuestionList(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let crmQuestion = response?.data?.items;
            const result = crmQuestion.reduce((acc, d) => {
              if (d.answer) {
                const value = { _id: d?._id, label: d?.answer, value: d?._id };
                acc.push(value);
              }
              return acc;
            }, []);

            this.crmQuestionList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getCampaignList() {
    return new Promise((resolve, reject) => {
      const obj = {
        crmQueId: [],
        numTypeId: 1,
      };

      this.campaignList = [];
      this._loaderService.start();

      this._leadsService.getCampaignList(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let campaign = response?.data?.items;
            const result = campaign.reduce((acc, d) => {
              if (d.marketingTitle) {
                const value = {
                  _id: d?._id,
                  label: d?.marketingTitle,
                  value: d?._id,
                };
                acc.push(value);
              }
              return acc;
            }, []);

            result.push({
              _id: 'other',
              label: 'Other',
              value: 'other',
            });

            this.campaignList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getMainStatus() {
    return new Promise((resolve, reject) => {
      const obj = {
        page: 1,
        limit: 1000,
        moduleId: this.data.moduleId,
      };

      this._loaderService.start();

      this._sharedService.getMainStatus(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let mainStatusList = response?.data?.items;
            let filteredData;

            if (this.data.moduleId == StatusConstant?.MainStatusId?.LEAD) {
              filteredData = mainStatusList.filter(
                (item) => item.title !== 'inventory'
              );
            } else {
              filteredData = mainStatusList;
            }

            const result = filteredData.reduce((acc, d) => {
              if (d.title) {
                const value = {
                  _id: d?._id,
                  label: d?.labels?.title
                    ? this._commonFunctionService.capitalizeName(
                        d?.labels?.title
                      )
                    : this._commonFunctionService.capitalizeName(d?.title),
                  value: d?._id,
                };
                acc.push(value);
              }
              return acc;
            }, []);

            this.mainStatusList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  getPropertyType() {
    return new Promise((resolve, reject) => {
      try {
        let propertyType = this.listStackingCodeJson?.luCode;
        let proprtyTypeList = [];

        for (let key in propertyType) {
          proprtyTypeList.push({ label: propertyType[key], value: key });
        }
        proprtyTypeList.push({ label: 'Unknown', value: '-1' });
        // Sort the list alphabetically by label
        proprtyTypeList.sort((a, b) => a.label.localeCompare(b.label));

        this.proprtyTypeList = proprtyTypeList;
        resolve(proprtyTypeList);
      } catch (error) {
        reject([]);
      }
    });
  }

  onDripList() {
    return new Promise((resolve, reject) => {
      const obj = {
        page: 1,
        limit: 1000,
        moduleId: this.data?.moduleId,
      };

      this._loaderService.start();

      this._dripService.listDropDown(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let dripList = response?.data?.items;
            const result = dripList.reduce((acc, d) => {
              if (d.name) {
                const value = {
                  _id: d?._id,
                  label: d?.name,
                  value: d?._id,
                };
                acc.push(value);
              }
              return acc;
            }, []);

            this.dripList = result;

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  // getTaskList() {
  //   return new Promise((resolve, reject) => {
  //     const obj = {
  //       moduleId: StatusConstant?.MainStatusId?.LEAD,
  //     };

  //     this._loaderService.start();

  //     this._taskService.getAllTask(obj).subscribe(
  //       (response) => {
  //         this._loaderService.stop();
  //         if (response && response.statusCode == 200) {
  //           this.taskList = response?.data;
  //           resolve(this.taskList);
  //         }
  //       },
  //       (err: ErrorModel) => {
  //         reject([]);
  //         this._loaderService.stop();
  //       }
  //     );
  //   });
  // }

  // taskNameList() {
  //   return new Promise((resolve, reject) => {
  //     const seenTitles = new Set();
  //     const result = this.taskList.reduce((acc, d) => {
  //       console.log('d ====>', d);

  //       if (!seenTitles.has(d?.title)) {
  //         seenTitles.add(d?.title);
  //         const value = {
  //           _id: d?._id,
  //           label: d?.title ? d?.title : 'N/A',
  //           value: d?.title,
  //         };
  //         acc.push(value);
  //       }
  //       return acc;
  //     }, []);

  //     resolve(result);
  //   });
  // }

  taskNameList(bool: boolean, searchString?) {
    return new Promise((resolve, reject) => {
      const obj = {
        moduleId: StatusConstant?.MainStatusId?.LEAD,
      };

      if (bool && searchString && searchString != '') {
        obj['searchString'] = searchString;
      }

      if (!bool) {
        this._loaderService.start();
      }

      this._taskService.getAllTask(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let taskList = response?.data;

            const seenTitles = new Set();
            const result = taskList.reduce((acc, d) => {
              if (!seenTitles.has(d?.title)) {
                seenTitles.add(d?.title);
                const value = {
                  _id: d?._id,
                  label: d?.title ? d?.title : 'N/A',
                  value: d?.title,
                };
                acc.push(value);
              }
              return acc;
            }, []);

            if (bool && searchString) {
              this.taskList.push(...result);
            } else {
              this.taskList = result;
            }

            if (this.isEdit) {
              let filteredValue = this.selectedFilter.filter(
                (a) => a.value == 'taskName'
              )[0];

              let index = this.selectedFilter.findIndex(
                (county) => county.value == 'taskName'
              );

              if (filteredValue) {
                let selectedOptions = filteredValue.selectedOption || [];
                let missingCounty;

                missingCounty = selectedOptions.filter(
                  (option) =>
                    !this.taskList.some(
                      (county) =>
                        county.value.toLowerCase() == option.toLowerCase()
                    )
                );

                if (missingCounty.length > 0) {
                  let newCities = missingCounty.map((county) => ({
                    _id: Math.random().toString(36).substr(2, 9),
                    label: county,
                    value: county,
                  }));
                  this.selectedFilter[index]?.options.push(...newCities);
                }
              }
            }
            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  taskTypeList() {
    return new Promise((resolve, reject) => {
      let taskType = this._commonFunctionService.taskType();
      const result = taskType.reduce((acc, d) => {
        const value = {
          _id: d?.id,
          label: d?.name,
          value: d?.id,
        };
        acc.push(value);

        return acc;
      }, []);

      resolve(result);
    });
  }

  leadDecomposeAddress(key) {
    return new Promise((resolve, reject) => {
      const obj = {
        key,
      };

      this._loaderService.start();

      this._leadsService.getLeadDecomposeAddress(obj).subscribe(
        (response) => {
          this._loaderService.stop();
          if (response && response.statusCode == 200) {
            let cityList = response?.data;

            const result = cityList.reduce((acc, d) => {
              const value = {
                _id: d,
                label: d,
                value: d,
              };
              acc.push(value);

              return acc;
            }, []);
            // switch (key) {
            //   case 'city':
            //     this.cityList = result;
            //     break;
            //   case 'state':
            //     this.stateList = result;
            //     break;
            //   case 'zip':
            //     this.zipList = result;
            //     break;

            //   default:
            //     break;
            // }

            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  async changeSelectedOperator(filter, index) {
    await this.changeValidation(index);

    this.selectedFilter[index].minVal = '';
    this.selectedFilter[index].maxVal = '';
    if (
      filter?.optionsType == 'DATE-RANGE' &&
      filter?.selectedOperator == 'is'
    ) {
      this.selectedFilter[index].conditionType = this.conditionalDate;
      this.selectedFilter[index].selectedCondition =
        this.conditionalDate[0]?.value;
    } else if (
      filter?.optionsType == 'DATE-RANGE' &&
      filter?.selectedOperator != 'is'
    ) {
      this.selectedFilter[index].conditionType = [];
      this.selectedFilter[index].selectedCondition = '';
    } else if (
      filter?.optionsType == 'TIME-RANGE' &&
      filter?.optionsType == 'TIME-RANGE-ZERO' &&
      filter?.value != 'mlsMarket' &&
      filter.value != 'ownershipDuration' &&
      filter?.value != 'incomingCallDuration' &&
      filter?.value != 'outgoingCallDuration' &&
      filter?.value != 'openLeadDuration' &&
      filter?.value != 'holding'
    ) {
      this.selectedFilter[index].conditionType = this.conditionalTime;
      this.selectedFilter[index].selectedCondition =
        this.conditionalTime[0]?.value;
    }

    // if (filter?.optionsType == 'DATE-RANGE') {
    //   this.changeConditionalDateRange(filter);
    // }
    if (
      filter?.optionsType == 'TIME-RANGE' ||
      filter?.optionsType == 'TIME-RANGE-ZERO'
    ) {
      this.changeConditionalTimeRange(filter);
    }
  }

  changeValidation(index) {
    const filter = this.selectedFilter[index];
    //for select
    if (filter.optionsType === 'SELECT' && this.tabIndex === 2) {
      filter.error =
        !filter.selectedOption || filter.selectedOption.length === 0;
    }

    //for operator
    if (!filter?.selectedOperator && this.tabIndex === 2) {
      filter.operatorError = true;
    } else {
      filter.operatorError = false;
    }

    //for date range
    if (filter.optionsType === 'DATE-RANGE' && this.tabIndex === 2) {
      // For start date
      if (!filter.minVal) {
        filter.error = filter.errorStartDate = true;
        filter.rangeErrorMsg = 'Please add Date';
      } else {
        filter.errorStartDate = false;
        if (filter.selectedOperator !== 'between') {
          filter.error = false;
          filter.rangeErrorMsg = '';
        }
      }

      // For end date
      if (!filter.maxVal && filter.selectedOperator === 'between') {
        filter.error = filter.errorEndDate = true;
        filter.rangeErrorMsg = 'Please add Date';
      } else {
        filter.errorEndDate = false;
        if (filter.minVal) {
          filter.error = false;
          filter.rangeErrorMsg = '';
        }
      }
    }

    //for date score
    if (filter.optionsType === 'DATE-SCORE' && this.tabIndex === 2) {
      // For start date
      if (!filter.minVal) {
        filter.error = filter.errorStartDate = true;
        filter.rangeErrorMsg = 'Please add Date';
      } else {
        filter.errorStartDate = false;
        if (filter.selectedOperator !== 'between') {
          filter.error = false;
          filter.rangeErrorMsg = '';
        }
      }
    }

    //for range
    if (filter.optionsType === 'RANGE' && this.tabIndex === 2) {
      // For min value
      if (
        filter.minVal == null ||
        filter.minVal == undefined ||
        filter.minVal == '' ||
        !filter.noMinValidatation
      ) {
        filter.error = filter.errorMinVal = true;
        filter.rangeErrorMsg = 'Please add value';
      } else {
        filter.errorMinVal = false;
        if (filter.selectedOperator !== 'between') {
          filter.error = false;
          filter.rangeErrorMsg = '';
        }
      }

      // For max value
      if (!filter.maxVal && filter.selectedOperator === 'between') {
        filter.error = filter.errorMaxVal = true;
        filter.rangeErrorMsg = 'Please add value';
      } else {
        filter.errorMaxVal = false;
        if (filter.minVal) {
          filter.error = false;
          filter.rangeErrorMsg = '';
        }
      }

      // For value error
      if (filter.minVal && filter.maxVal && filter.minVal >= filter.maxVal) {
        filter.error = filter.errorMaxVal = true;
        filter.rangeErrorMsg = 'Max. value less than or equal to Min. value';
      }
    }

    // For time range and multi-select
    if (
      (filter.optionsType === 'TIME-RANGE' ||
        filter.optionsType === 'TIME-RANGE-ZERO' ||
        filter.optionsType === 'MULTI-SELECT' ||
        filter.optionsType === 'MULTI-SELECT-TITLE') &&
      this.tabIndex === 2
    ) {
      if (
        !filter.selectedOption ||
        filter.selectedOption == '' ||
        filter.selectedOption.length == 0
      ) {
        filter.error = filter.errorMinVal = true;
        filter.rangeErrorMsg = 'Please add value';
        if (filter?.value == 'emailCampaign') {
          let i = this.filterParameters.findIndex(
            (x) => x.value == 'emailStats'
          );
          if (i >= 0) {
            this.filterParameters[i].disabled = true;
            this.filterParameters[i].checked = false;
            let checkExistingValue = this.selectedFilter.findIndex(
              (x) => x.value == 'emailStats'
            );
            if (checkExistingValue >= 0) {
              this.selectedFilter.splice(checkExistingValue, 1);
            }
          }
        }
        if (filter?.value == 'smsCampaign') {
          let i = this.filterParameters.findIndex((x) => x.value == 'smsStats');
          if (i >= 0) {
            this.filterParameters[i].disabled = true;
            this.filterParameters[i].checked = false;
            let checkExistingValue = this.selectedFilter.findIndex(
              (x) => x.value == 'smsStats'
            );
            if (checkExistingValue >= 0) {
              this.selectedFilter.splice(checkExistingValue, 1);
            }
          }
        }
      } else {
        filter.error = filter.errorMinVal = false;
        filter.rangeErrorMsg = '';
      }

      if (
        filter.optionsType === 'TIME-RANGE' ||
        filter.optionsType === 'TIME-RANGE-ZERO'
      ) {
        filter.error = filter.conditionError = false;
        filter.rangeErrorMsg = '';
        if (!filter.selectedCondition) {
          filter.error = filter.conditionError = true;
          filter.rangeErrorMsg = 'Please add value';
        }
        if (!filter.selectedOption) {
          filter.error = filter.errorMinVal = true;
          filter.rangeErrorMsg = 'Please add value';
        }
      }
    }
  }

  async getGroups(moduleId) {
    const promise = new Promise((resolve, reject) => {
      this._loaderService.start();
      const obj = {
        page: 1,
        limit: 100,
        moduleId: moduleId,
        isFromList: true,
      };
      this._preferenceService.getGroups(obj).subscribe(
        async (response: ResponseModel) => {
          if (response.statusCode === 200 && response.data.items.length > 0) {
            let groups = this._commonFunctionService.orderItems(
              response.data.items,
              'order'
            );

            let promises = groups.map((x) => this.getQuestions([x?._id]));
            const result = await Promise.all(promises);
            const flattenedResults = [].concat(...result);
            resolve(flattenedResults);
          } else {
            this._loaderService.stop();
            resolve([]);
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
          if (err.error) {
            const error: ResponseModel = err.error;
            this._toastrService.error(error.message, '');
          } else {
            this._toastrService.error(this.messageConstant.unknownError, '');
          }
          reject(err);
        }
      );
    });
    return promise;
  }

  getQuestions(groupId) {
    const promise = new Promise((resolve, reject) => {
      this._loaderService.start();
      const obj = {
        mainGroupId: groupId,
        page: 1,
        limit: 1000,
      };

      this._preferenceService.getFields(obj).subscribe(
        (response: ResponseModel) => {
          if (response.statusCode === 200) {
            let arr = response?.data?.items || [];
            let reduceOptions = [];

            let questionsResult = arr.map((e) => {
              if (e?.options && e?.options.length > 0) {
                reduceOptions = e?.options.reduce((acc, d) => {
                  if (d.key && e.questionType != 'DROPDOWN') {
                    const value = {
                      _id: d?.value,
                      label: d?.key,
                      value: d?.value,
                    };
                    acc.push(value);
                  }
                  if (d.key && e.questionType == 'DROPDOWN') {
                    const value = {
                      _id: d?.key,
                      label: d?.key,
                      value: d?.key,
                    };
                    acc.push(value);
                  }
                  return acc;
                }, []);
              }

              let finalOptions = {
                _id: e._id,
                label: e.questionTitle,
                value: e._id,
                type: e.questionType,
                options: reduceOptions,
                selectedOperator: '',
              };
              if (
                e.questionType === 'TEXT' ||
                e.questionType === 'MULTILINE_TEXT'
              ) {
                finalOptions['operator'] = [
                  {
                    label: 'Has',
                    value: 'is',
                  },
                ];
                finalOptions['selectedOperator'] = 'is';
                finalOptions['options'] = [
                  {
                    _id: 'yes',
                    label: 'Data Input',
                    value: 'yes',
                  },
                  {
                    _id: 'no',
                    label: 'No Data Input',
                    value: 'no',
                  },
                ];
              }
              if (
                // e.questionType === 'TEXT' ||
                // e.questionType === 'MULTILINE_TEXT' ||
                e.questionType === 'CHECK_LIST' ||
                e.questionType === 'RADIO_BUTTON' ||
                e.questionType === 'DROPDOWN'
              ) {
                finalOptions['operator'] = [
                  {
                    label: 'Equals',
                    value: 'is',
                  },
                ];
                finalOptions['selectedOperator'] = 'is';
              }
              if (
                e.questionType === 'NUMBER' ||
                e.questionType === 'CURRENCY'
              ) {
                finalOptions['operator'] = [
                  {
                    label: 'Equal',
                    value: 'is',
                  },
                  {
                    label: 'More than',
                    value: 'morethan',
                  },
                  {
                    label: 'Less than',
                    value: 'lessthan',
                  },
                  {
                    label: 'Between',
                    value: 'between',
                  },
                ];
                finalOptions['selectedOperator'] = 'is';
              }
              if (e.questionType === 'DATE') {
                finalOptions['operator'] = [
                  {
                    label: 'More than',
                    value: 'morethan',
                  },
                  {
                    label: 'Less than',
                    value: 'lessthan',
                  },
                  {
                    label: 'Between',
                    value: 'between',
                  },
                ];
                finalOptions['selectedOperator'] = 'morethan';
              }
              return finalOptions;
            });
            this._loaderService.stop();
            resolve(questionsResult);
          } else {
            this._loaderService.stop();
            resolve([]);
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
          if (err.error) {
            const error: ResponseModel = err.error;
            this._toastrService.error(error.message, '');
          } else {
            this._toastrService.error(this.messageConstant.unknownError, '');
          }
          reject(err);
        }
      );
    });

    return promise;
  }

  getDeadReasons() {
    return new Promise((resolve, reject) => {
      this.deadReasons = [];
      this._loaderService.start();
      this._leadsService.getDeadReason({}).subscribe(
        (response: ResponseModel) => {
          if (response.statusCode == 200) {
            this.deadReasons = response?.data;
            const result = this.deadReasons.reduce((acc, d) => {
              if (d.name) {
                const value = { _id: d?._id, label: d?.name, value: d?._id };
                acc.push(value);
              }
              return acc;
            }, []);
            result.push({
              _id: 'others',
              label: 'Others',
              value: 'others',
            });

            this.deadReasons = result;
            resolve(result);
            this._loaderService.stop();
          }
        },
        (err: ErrorModel) => {
          reject([]);
          this._loaderService.stop();
        }
      );
    });
  }

  transformAmount(element) {
    var value = element.target.value.split('.');
    if (element.target.value && value[1]) {
      if (value[1].length == 2) {
        element.target.value = element.target.value;
      } else if (value[1].length == 1) {
        element.target.value = element.target.value + '0';
      } else {
        element.target.value = element.target.value + '00';
      }
    } else if (element.target.value != '$ ') {
      element.target.value = value[0] + '.00';
    }
  }

  // getOutgoingCallResult() {
  //   let outgoingCallResultOPtion = [
  //     ...this._commonFunctionService.callConnectedStatus(),
  //     ...this._commonFunctionService.callNotConnectedStatus(),
  //   ];

  //   const result = outgoingCallResultOPtion.reduce((acc, d) => {
  //     if (d.name) {
  //       const value = { _id: d?.value, label: d?.name, value: d?.value };
  //       acc.push(value);
  //     }
  //     return acc;
  //   }, []);

  //   return result;
  // }

  getOutgoingCallResult() {
    let callConnectedStatus = this._commonFunctionService.callConnectedStatus();
    let callNotConnectedStatus =
      this._commonFunctionService.callNotConnectedStatus();

    let callConnectedStatusItem = callConnectedStatus.reduce((acc, d) => {
      if (d.name) {
        const value = { _id: d?.value, label: d?.name, value: d?.value };
        acc.push(value);
      }
      return acc;
    }, []);

    let callNotConnectedStatusItem = callNotConnectedStatus.reduce((acc, d) => {
      if (d.name) {
        const value = { _id: d?.value, label: d?.name, value: d?.value };
        acc.push(value);
      }
      return acc;
    }, []);

    const result = [
      {
        label: 'Call Connected',
        value: 'callConnectedStatus',
        items: callConnectedStatusItem,
      },
      {
        label: 'Call Not Connected',
        value: 'callNotConnectedStatus',
        items: callNotConnectedStatusItem,
      },
    ];

    return result;
  }

  // getBuyerCitiesAndZips(type) {
  //   return new Promise((resolve, reject) => {
  //     this._loaderService.start();
  //     let obj = {
  //       updateType: type === 'city' ? 1 : 2,
  //       filterData: {},
  //       isFilter: true,
  //       buyerIds: [],
  //     };

  //     this._buyersService.getTagList(obj).subscribe(
  //       (response: ResponseModel) => {
  //         this._loaderService.stop();
  //         if (response.statusCode == 200) {
  //           let arr = response.data.items;

  //           const result = arr.reduce((acc, d) => {
  //             if (d) {
  //               const value = { _id: d, label: d, value: d };

  //               acc.push(value);
  //             }

  //             return acc;
  //           }, []);

  //           if (type === 'zip') {
  //             this.allZipList = result;
  //             this.filteredZip = this.zipControl.valueChanges.pipe(
  //               startWith(null),
  //               map((tag) =>
  //                 tag ? this._filter(tag, 'zip') : this.allZipList.slice()
  //               )
  //             );
  //           }

  //           if (type === 'city') {
  //             this.allCitiesList = result;
  //             this.filteredCities = this.cityControl.valueChanges.pipe(
  //               startWith(null),
  //               map((tag) =>
  //                 tag ? this._filter(tag, 'city') : this.allCitiesList.slice()
  //               )
  //             );
  //           }
  //           resolve(result);
  //         }
  //       },
  //       (err: ErrorModel) => {
  //         this._loaderService.stop();
  //       }
  //     );
  //   });
  // }
  // private _filter(value, type) {
  //   let filterValue;
  //   if (value._id) {
  //     filterValue = value.label?.toLowerCase();
  //   } else {
  //     filterValue = value.toLowerCase();
  //   }

  //   if (type === 'city') {
  //     return this.allCitiesList.filter(
  //       (tag) => tag.label.toLowerCase().indexOf(filterValue) === 0
  //     );
  //   } else if (type === 'zip') {
  //     return this.allZipList.filter(
  //       (tag) => tag.label.toLowerCase().indexOf(filterValue) === 0
  //     );
  //   } else if (type === 'tag') {
  //     return this.allTagList.filter(
  //       (tag) => tag.label.toLowerCase().indexOf(filterValue) === 0
  //     );
  //   }
  // }
  getUsedColorIndex() {
    this._loaderService.start();
    this._taggingService.getUsedTagsColor(this.tagType).subscribe(
      (response: ResponseModel) => {
        if (response.statusCode == 200) {
          this._loaderService.stop();
          this.unavailableColorIndex = response?.data;
          this.unavailableColorIndexResponse = [];
          let i, j;

          for (i = 0; i < this.colorCodes.length; i++) {
            this.unavailableColorIndexResponse.push({ _id: i, count: 0 });

            for (j = 0; j < this.unavailableColorIndex.length; j++) {
              if (this.unavailableColorIndex[j]._id == i) {
                this.unavailableColorIndexResponse[i].count =
                  this.unavailableColorIndex[j].count;
                break;
              }
            }
          }

          this.unavailableColorIndexResponse.sort(
            (a, b) => parseFloat(a.count) - parseFloat(b.count)
          );
        }
      },
      (err: ErrorModel) => {
        this._loaderService.stop();
      }
    );
  }
  getCampaigns(obj, type) {
    return new Promise((resolve, reject) => {
      obj = {
        ...obj,
        moduleId: this.data.moduleId,
      };
      this._loaderService.start();
      this._buyersService.getCampaignsSuggestions(obj, type).subscribe(
        (response: ResponseModel) => {
          if (response?.statusCode == 200) {
            this._loaderService.stop();
            const arr = response.data.items;
            const result = arr.reduce((acc, d) => {
              if (d) {
                const value = {
                  label: type == 'email' ? d.name : d.campaignName,
                  value: d._id,
                };
                acc.push(value);
              }
              return acc;
            }, []);
            this.suggestionList = result;
            resolve(result);
          }
        },
        (err: ErrorModel) => {
          reject(false);
          if (err.error) {
            this._loaderService.stop();
            const error: ResponseModel = err.error;
            this._toastrService.error(error.message, '');
          } else {
            this._toastrService.error(this.messageConstant.unknownError, '');
          }
        }
      );
    });
  }
  getVendorCategory() {
    return new Promise((resolve, reject) => {
      this._accountingService.getVendorCategory({}).subscribe(
        (response: ResponseModel) => {
          this._loaderService.stop();
          if (response?.statusCode == 200) {
            let category = [];
            response?.data?.filter((x) => {
              category.push({ label: x.name, value: x._id });
            });
            return resolve(category);
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
          if (err.error) {
            const error: ResponseModel = err.error;
            this._toastrService.error(error.message, '');
          } else {
            this._toastrService.error(this.messageConstant.unknownError, '');
          }
          return resolve([]);
        }
      );
    });
  }
  getVendorTags() {
    return new Promise((resolve, reject) => {
      this._loaderService.start();
      let obj = {
        updateType: 1,
        isFilter: true,
      };

      this._vendorsService.getTagList(obj).subscribe(
        (response: ResponseModel) => {
          this._loaderService.stop();
          if (response.statusCode == 200) {
            const arr = response.data.items;
            const result = arr.reduce((acc, d) => {
              if (d) {
                const value = { _id: d, label: d, value: d };
                acc.push(value);
              }
              return acc;
            }, []);
            return resolve(result);
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
        }
      );
    });
  }
  async fetchFilteredOptions($event, filter, index) {
    if ($event == '') {
      return;
    }
    let data: any = [];
    let searchString = $event.filter;
    switch (filter.value) {
      case 'targetCities':
        data = await this.getTargetList(2, $event);
        break;
      case 'targetZip':
        data = await this.getTargetList(4, $event);
        break;
      // case 'targetStates':
      //   data = await this.getTargetList(3, $event);
      //   break;
      case 'targetCounty':
        data = await this.getTargetList(1, $event);
        break;
      case 'taskName':
        data = await this.taskNameList(true, searchString);
        break;
    }

    if (data.length > 0) {
      this.selectedFilter[index].options = data;
      if (filter.value == 'targetCounty') {
        try {
          let filteredValue = this.selectedFilter.filter(
            (a) => a.value === 'targetCounty'
          )[0];
          let index = this.selectedFilter.findIndex(
            (county) => county.value == 'targetCounty'
          );

          if (filteredValue) {
            let selectedOptions = filteredValue.selectedOption || [];

            let missingCounty = selectedOptions.filter(
              (option) =>
                !this.allCountiesList.some(
                  (county) =>
                    county.value.toLowerCase() === option.toLowerCase()
                )
            );

            if (missingCounty.length > 0) {
              let newCities = missingCounty.map((county) => ({
                _id: Math.random().toString(36).substr(2, 9),
                label: county,
                value: county,
              }));
              this.selectedFilter[index]?.options.push(...newCities);
            }
          }
        } catch (error) {
          console.log('Error :>> ', error);
        }
      }
      if (filter.value == 'targetCities') {
        try {
          let filteredValue = this.selectedFilter.filter(
            (a) => a.value === 'targetCities'
          )[0];
          let index = this.selectedFilter.findIndex(
            (city) => city.value == 'targetCities'
          );

          if (filteredValue) {
            let selectedOptions = filteredValue.selectedOption || [];

            let missingCities = selectedOptions.filter(
              (option) =>
                !this.allCitiesList.some(
                  (city) => city.value.toLowerCase() === option.toLowerCase()
                )
            );

            if (missingCities.length > 0) {
              let newCities = missingCities.map((city) => ({
                _id: Math.random().toString(36).substr(2, 9),
                label: city,
                value: city,
              }));
              this.selectedFilter[index]?.options.push(...newCities);
            }
          }
        } catch (error) {
          console.log('Error :>> ', error);
        }
      }
      // if(filter.value == 'targetStates'){
      //   try {
      //     let filteredValue = this.selectedFilter.filter(
      //       (a) => a.value === 'targetStates'
      //     )[0];
      //     let index = this.selectedFilter.findIndex(states => states.value == 'targetStates');

      //     if (filteredValue) {
      //       let selectedOptions = filteredValue.selectedOption || [];

      //       let missingStates = selectedOptions.filter(
      //         (option) =>
      //           !this.allStatesList.some(
      //             (states) =>
      //               states.value.toLowerCase() === option.toLowerCase()
      //           )
      //       );

      //       if (missingStates.length > 0) {
      //         let newCities = missingStates.map((states) => ({
      //           _id:Math.random().toString(36).substr(2, 9),
      //           label: states,
      //           value: states,
      //         }));
      //         this.selectedFilter[index]?.options.push(...newCities);
      //       }
      //     }
      //   } catch (error) {
      //     console.log('Error :>> ', error);
      //   }
      // }
      if (filter.value == 'targetZip') {
        try {
          let filteredValue = this.selectedFilter.filter(
            (a) => a.value === 'targetZip'
          )[0];
          let index = this.selectedFilter.findIndex(
            (zip) => zip.value == 'targetZip'
          );

          if (filteredValue) {
            let selectedOptions = filteredValue.selectedOption || [];

            let missingZips = selectedOptions.filter(
              (option) =>
                !this.allZipList.some(
                  (zip) => zip.value.toLowerCase() === option.toLowerCase()
                )
            );

            if (missingZips.length > 0) {
              let newCities = missingZips.map((zip) => ({
                _id: Math.random().toString(36).substr(2, 9),
                label: zip,
                value: zip,
              }));
              this.selectedFilter[index]?.options.push(...newCities);
            }
          }
        } catch (error) {
          console.log('Error :>> ', error);
        }
      }
      if (filter.value == 'taskName') {
        let filteredValue = this.selectedFilter.filter(
          (a) => a.value === 'taskName'
        )[0];
        let index = this.selectedFilter.findIndex(
          (task) => task.value == 'taskName'
        );

        if (filteredValue) {
          let selectedOptions = filteredValue.selectedOption || [];

          let missingTask = selectedOptions.filter(
            (option) =>
              !this.taskList.some(
                (task) => task.value.toLowerCase() === option.toLowerCase()
              )
          );

          if (missingTask.length > 0) {
            let newCities = missingTask.map((task) => ({
              _id: Math.random().toString(36).substr(2, 9),
              label: task,
              value: task,
            }));
            this.selectedFilter[index]?.options.push(...newCities);
          }
        }
      }
    }
  }

  getTargetList(type: number, $event: string) {
    return new Promise((resolve, reject) => {
      this._loaderService.start();
      const obj = {
        page: 1,
        limit: this.targetGeographicInfoLimit,
        type: type,
        // searchString: $event,
      };
      if ($event != '') {
        obj['searchString'] = $event;
      }
      this._buyersService.getTargetList(obj).subscribe(
        (response: ResponseModel) => {
          this._loaderService.stop();
          if (response?.statusCode === 200) {
            const data = response.data.items.map((v) => ({
              _id: v._id,
              label:
                type == 2
                  ? this._commonFunctionService.titleCaseToWord(v.label)
                  : v.label,
              value:
                type == 2
                  ? this._commonFunctionService.titleCaseToWord(v.value)
                  : v.value,
            }));

            switch (type) {
              case 1: // Property_County_Name
                this.allCountiesList = data;
                if (this.isEdit) {
                  try {
                    let filteredValue = this.selectedFilter.filter(
                      (a) => a.value === 'targetCounty'
                    )[0];
                    let index = this.selectedFilter.findIndex(
                      (county) => county.value == 'targetCounty'
                    );

                    if (filteredValue) {
                      let selectedOptions = filteredValue.selectedOption || [];

                      let missingCounty = selectedOptions.filter(
                        (option) =>
                          !this.allCountiesList.some(
                            (county) =>
                              county.value.toLowerCase() ===
                              option.toLowerCase()
                          )
                      );

                      if (missingCounty.length > 0) {
                        let newCities = missingCounty.map((county) => ({
                          _id: Math.random().toString(36).substr(2, 9),
                          label: county,
                          value: county,
                        }));
                        this.selectedFilter[index]?.options.push(...newCities);
                      }
                    }
                  } catch (error) {
                    console.log('Error :>> ', error);
                  }
                }
                this.filteredCounties = this.countyControl.valueChanges.pipe(
                  startWith(null),
                  map((tag) =>
                    tag
                      ? this._filter(tag, 'county')
                      : this.allCountiesList.slice()
                  )
                );
                break;
              case 2: // Property_City
                this.allCitiesList = data;
                if (this.isEdit) {
                  try {
                    let filteredValue = this.selectedFilter.filter(
                      (a) => a.value === 'targetCities'
                    )[0];
                    let index = this.selectedFilter.findIndex(
                      (city) => city.value == 'targetCities'
                    );

                    if (filteredValue) {
                      let selectedOptions = filteredValue.selectedOption || [];

                      let missingCities = selectedOptions.filter(
                        (option) =>
                          !this.allCitiesList.some(
                            (city) =>
                              city.value.toLowerCase() === option.toLowerCase()
                          )
                      );

                      if (missingCities.length > 0) {
                        let newCities = missingCities.map((city) => ({
                          _id: Math.random().toString(36).substr(2, 9),
                          label: city,
                          value: city,
                        }));
                        this.selectedFilter[index]?.options.push(...newCities);
                      }
                    }
                  } catch (error) {
                    console.log('Error :>> ', error);
                  }
                }

                this.filteredCities = this.cityControl.valueChanges.pipe(
                  startWith(null),
                  map((tag) =>
                    tag ? this._filter(tag, 'city') : this.allCitiesList.slice()
                  )
                );
                break;
              case 3: // Property_State
                this.allStatesList = data;
                // if (this.isEdit) {
                //   try {
                //     let filteredValue = this.selectedFilter.filter(
                //       (a) => a.value === 'targetStates'
                //     )[0];
                //     let index = this.selectedFilter.findIndex(states => states.value == 'targetStates');

                //     if (filteredValue) {
                //       let selectedOptions = filteredValue.selectedOption || [];

                //       let missingStates = selectedOptions.filter(
                //         (option) =>
                //           !this.allStatesList.some(
                //             (states) =>
                //               states.value.toLowerCase() === option.toLowerCase()
                //           )
                //       );

                //       if (missingStates.length > 0) {
                //         let newCities = missingStates.map((states) => ({
                //           _id:Math.random().toString(36).substr(2, 9),
                //           label: states,
                //           value: states,
                //         }));
                //         this.selectedFilter[index]?.options.push(...newCities);
                //       }
                //     }
                //   } catch (error) {
                //     console.log('Error :>> ', error);
                //   }
                // }
                this.filteredStates = this.stateControl.valueChanges.pipe(
                  startWith(null),
                  map((tag) =>
                    tag
                      ? this._filter(tag, 'state')
                      : this.allStatesList.slice()
                  )
                );
                break;
              case 4: // Property_Zip_Code
                this.allZipList = data;
                if (this.isEdit) {
                  try {
                    let filteredValue = this.selectedFilter.filter(
                      (a) => a.value === 'targetZip'
                    )[0];
                    let index = this.selectedFilter.findIndex(
                      (zip) => zip.value == 'targetZip'
                    );

                    if (filteredValue) {
                      let selectedOptions = filteredValue.selectedOption || [];

                      let missingZips = selectedOptions.filter(
                        (option) =>
                          !this.allZipList.some(
                            (zip) =>
                              zip.value.toLowerCase() === option.toLowerCase()
                          )
                      );
                      if (missingZips.length > 0) {
                        let newCities = missingZips.map((zip) => ({
                          _id: Math.random().toString(36).substr(2, 9),
                          label: zip,
                          value: zip,
                        }));
                        this.selectedFilter[index]?.options.push(...newCities);
                      }
                    }
                  } catch (error) {
                    console.log('Error :>> ', error);
                  }
                }
                this.filteredZip = this.zipControl.valueChanges.pipe(
                  startWith(null),
                  map((tag) =>
                    tag ? this._filter(tag, 'zip') : this.allZipList.slice()
                  )
                );
                break;
            }
            return resolve(data);
          } else {
            reject(response.message);
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
          this._toastrService.error(
            err.error?.message || this.messageConstant.unknownError,
            ''
          );
          reject(err);
        }
      );
    });
  }

  private _filter(value, type) {
    let filterValue;
    if (value._id) {
      filterValue = isNaN(value.label)
        ? value.label?.toLowerCase()
        : value.label;
    } else {
      filterValue = isNaN(value) ? value.toLowerCase() : value;
    }

    if (type === 'city') {
      return this.allCitiesList.filter(
        (tag) => tag.label.toLowerCase().indexOf(filterValue) === 0
      );
    } else if (type === 'zip') {
      return this.allZipList.filter(
        (tag) => tag.label?.toString().indexOf(filterValue?.toString()) === 0
      );
    } else if (type === 'county') {
      return this.allCountiesList.filter(
        (tag) => tag.label?.toString().indexOf(filterValue?.toString()) === 0
      );
    } else if (type === 'state') {
      return this.allStatesList.filter(
        (tag) => tag.label?.toString().indexOf(filterValue?.toString()) === 0
      );
    }
  }

  getUserDetails() {
    this._userService.getUserDetails({}).subscribe(
      async (response) => {
        if (response.statusCode == 200) {
          this.userData = response?.data;
          await this.getSubUserList();
          await this.getUser();
          // await this.getTaskList();
          await this.patchEditData();
          if (this.userData?._id != undefined && !this.isEdit) {
            this.userPermissionMain = [
              {
                _id: this.userData?._id,
                name: this.userData?.firstName + ' ' + this.userData?.lastName,
                permission: 1,
                profileImage: this.userData?.profileImage,
                createdAt: this.userData?.createdAt,
              },
            ];

            this.userPermission = this.userPermissionMain.map((item) => ({
              userId: item?._id,
              permission: item?.permission,
            }));

            this.isUserView = this.userPermission.some(
              (record) => record.permission === 1
            );
          }
        }
      },
      (err: ErrorModel) => {}
    );
  }

  patchEditData() {
    if (this.isEdit) {
      this.isSaveFilter = this.viewMode
        ? false
        : this.data?.filterRecord?._id
        ? true
        : false;
      let filterRecord = this.data.filterRecord?.filterData;
      let parseRecord =
        filterRecord && filterRecord.length > 0 ? JSON.parse(filterRecord) : [];
      parseRecord.map((x) => {
        for (const key in x) {
          this.filterParameters.map((y, i) => {
            if (y?.value == key) {
              if (
                key == 'leadCustomQuestions' ||
                key == 'buyerCustomQuestions'
              ) {
                y.masterOptions = x[key]['masterOptions'];
                y.masterOptions.forEach((z) => {
                  if (z.type == 'DATE') {
                    z['minVal'] = new Date(z['minVal']);
                    z['maxVal'] = new Date(z['maxVal']);
                  }
                });
              }
              y.selectedOperator = x[key]['operator'];
              y.selectedOption = x[key]['value'];
              if (y.optionsType == 'DATE-RANGE') {
                y.minVal = new Date(x[key]['selectedStartDate']);
                y.maxVal = new Date(x[key]['selectedEndDate']);
              }

              if (y.optionsType == 'DATE-SCORE') {
                y.minVal = new Date(x[key]['selectedStartDate']);
              }
              if (y.optionsType == 'RANGE') {
                y.minVal = x[key]['minVal'];
                y.maxVal = x[key]['maxVal'];
              }

              let a = { target: { checked: true } };
              this.checkedValue(a, i, x);
            }
          });
          this.filterParametersMain.map((y, i) => {
            if (y?.value == key) {
              if (
                key == 'leadCustomQuestions' ||
                key == 'buyerCustomQuestions'
              ) {
                y.masterOptions = x[key]['masterOptions'];
                y.masterOptions.forEach((z) => {
                  if (z.type == 'DATE') {
                    z['minVal'] = new Date(z['minVal']);
                    z['maxVal'] = new Date(z['maxVal']);
                  }
                });
              }
              y.selectedOperator = x[key]['operator'];
              y.selectedOption = x[key]['value'];
              if (y.optionsType == 'DATE-RANGE') {
                y.minVal = new Date(x[key]['selectedStartDate']);
                y.maxVal = new Date(x[key]['selectedEndDate']);
              }
              if (y.optionsType == 'RANGE') {
                y.minVal = x[key]['minVal'];
                y.maxVal = x[key]['maxVal'];
              }

              let a = { target: { checked: true } };
              this.checkedValue(a, i, x);
            }
          });
          this.editFilterData = x;
        }
      });

      this.filterDetailForm.patchValue({
        filterTitle: this.data?.filterRecord?.title
          ? this.data?.filterRecord?.title
          : '',
        filterDescription: this.data?.filterRecord?.description
          ? this.data?.filterRecord?.description
          : '',
      });
    }
  }

  getAddressFilter(key) {
    return new Promise((resolve, reject) => {
      this._loaderService.start();

      this._listBuildingService.getAddressFilters().subscribe(
        (response: any) => {
          let options = [];
          this._loaderService.stop();
          if (response?.code == 200) {
            let data = response?.data?.[key];

            if (key == 'counties') {
              let countyState = '';
              data?.filter((x, i) => {
                countyState = x?.countyName?.split(',')?.[1] || '';
                options.push({
                  _id: x?.countyName,
                  label: x?.countyName || 'N/A',
                  value: countyState ? `${x?.fips},${countyState}` : x?.fips,
                });
              });
            } else {
              data?.filter((x) => {
                options.push({ _id: x, label: x || 'N/A', value: x });
              });
            }
          }
          return resolve(options || []);
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
          return resolve([]);
        }
      );
    });
  }

  getBuyerCitiesAndZips(type) {
    return new Promise((resolve, reject) => {
      this._loaderService.start();

      let obj = {
        filterData: {},
        isFilter: true,
        buyerIds: [],
      };

      if (type === 'city') {
        obj['updateType'] = 1;
      } else if (type === 'zip') {
        obj['updateType'] = 2;
      } else if (type === 'Counties') {
        obj['updateType'] = 4;
      } else if (type === 'States') {
        obj['updateType'] = 5;
      }

      this._buyersService.getTagList(obj).subscribe(
        (response: ResponseModel) => {
          this._loaderService.stop();
          if (response.statusCode == 200) {
            let arr = response.data.items;

            const result = arr.reduce((acc, d) => {
              if (d) {
                if (type === 'States') {
                  const value = { _id: d, label: d.toUpperCase(), value: d };
                  acc.push(value);
                } else {
                  const value = {
                    _id: d,
                    label: this._commonFunctionService.titleCaseToWord(d),
                    value: d,
                  };
                  acc.push(value);
                }
              }

              return acc;
            }, []);

            if (type === 'zip') {
              this.allZipList = result;

              this.filteredZip = this.zipControl.valueChanges.pipe(
                startWith(null),
                map((tag) =>
                  tag ? this._filter(tag, 'zip') : this.allZipList.slice()
                )
              );
            }

            if (type === 'city') {
              this.allCitiesList = result;
              this.filteredCities = this.cityControl.valueChanges.pipe(
                startWith(null),
                map((tag) =>
                  tag ? this._filter(tag, 'city') : this.allCitiesList.slice()
                )
              );
            }

            if (type === 'Counties') {
              this.allCountiesList = result;
              this.filteredCounties = this.cityControl.valueChanges.pipe(
                startWith(null),
                map((tag) =>
                  tag
                    ? this._filter(tag, 'Counties')
                    : this.allCountiesList.slice()
                )
              );
            }

            if (type === 'States') {
              this.allStatesList = result;
              this.filteredStates = this.cityControl.valueChanges.pipe(
                startWith(null),
                map((tag) =>
                  tag ? this._filter(tag, 'States') : this.allStatesList.slice()
                )
              );
            }
            resolve(result);
          }
        },
        (err: ErrorModel) => {
          this._loaderService.stop();
        }
      );
    });
  }

  getPools() {
    return new Promise((resolve, reject) => {
      try {
        let optionsObject = this.listStackingCodeJson?.pool;
        let options = [];

        for (let key in optionsObject) {
          options.push({ label: optionsObject[key], value: key });
        }

        // Sort the list alphabetically by label
        options.sort((a, b) => a.label.localeCompare(b.label));
        resolve(options);
      } catch (error) {
        reject([]);
      }
    });
  }

  getGarage() {
    return new Promise((resolve, reject) => {
      try {
        let optionsObject = this.listStackingCodeJson?.garageType;
        let options = [];

        for (let key in optionsObject) {
          options.push({ label: optionsObject[key], value: key });
        }

        // Sort the list alphabetically by label
        options.sort((a, b) => a.label.localeCompare(b.label));
        resolve(options);
      } catch (error) {
        reject([]);
      }
    });
  }

  getBasement() {
    return new Promise((resolve, reject) => {
      try {
        let optionsObject = this.listStackingCodeJson?.basement;
        let options = [];

        for (let key in optionsObject) {
          options.push({ label: optionsObject[key], value: key });
        }

        // Sort the list alphabetically by label
        options.sort((a, b) => a.label.localeCompare(b.label));
        resolve(options);
      } catch (error) {
        reject([]);
      }
    });
  }
}
